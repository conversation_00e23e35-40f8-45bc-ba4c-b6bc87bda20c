# Travel Accommodation Booking Platform Documentation

Welcome to the comprehensive documentation for the Travel Accommodation Booking Platform - a modern, scalable web API built with .NET 9.0 and Clean Architecture principles.

## 📚 Documentation Structure

This documentation is organized into multiple focused sections to help you understand, develop, and maintain the system:

### Core Documentation

- **[Project Overview](01-project-overview.md)** - Introduction, features, and getting started
- **[Architecture](02-architecture.md)** - System architecture, design patterns, and project structure
- **[API Documentation](03-api-documentation.md)** - Comprehensive API reference and usage examples

### Technical Documentation

- **[Database](04-database.md)** - Database schema, entities, and relationships
- **[Development Guide](05-development.md)** - Setup, coding standards, and contribution guidelines
- **[Deployment](06-deployment.md)** - Docker, CI/CD, and production deployment

### Operations Documentation

- **[Security](07-security.md)** - Authentication, authorization, and security best practices
- **[Testing](08-testing.md)** - Testing strategies, structure, and execution
- **[Monitoring & Logging](09-monitoring-logging.md)** - Logging configuration and troubleshooting

## 🚀 Quick Start

1. **New to the project?** Start with [Project Overview](01-project-overview.md)
2. **Setting up development?** Check [Development Guide](05-development.md)
3. **Need API reference?** See [API Documentation](03-api-documentation.md)
4. **Deploying the system?** Follow [Deployment Guide](06-deployment.md)

## 🏗️ System Overview

The Travel Accommodation Booking Platform is a comprehensive booking system that enables:

- **User Management** - Registration, authentication, and profile management
- **Hotel & Room Management** - Complete hotel and room inventory system
- **Booking System** - End-to-end booking workflow with real-time availability
- **Review System** - User reviews and ratings for hotels
- **Admin Panel** - Administrative functions and system management

## 🛠️ Technology Stack

- **Backend**: .NET 9.0, ASP.NET Core Web API
- **Database**: SQL Server with Entity Framework Core
- **Authentication**: RBAC JWT with Argon2 password hashing
- **Architecture**: Clean Architecture
- **Containerization**: Docker, DockerHub
- **Orchestration**: Docker Swarm
- **Infrastructure**: AWS EC2
- **CI/CD**: GitHub Actions
- **Monitoring**: Serilog, Promethues, and Grafana
- **Caching**: In-Memory Cache, and HTTP Cache

## 📋 Key Features

### For Users

- Secure registration and authentication
- Hotel and room search with filters
- Real-time booking with availability checking
- Review and rating system

### For Administrators

- User management and administration
- Hotel and room inventory management
- Booking oversight and management

### For Developers

- Clean, maintainable codebase
- Comprehensive test coverage
- API versioning and documentation
- Security best practices
- Scalable architecture

## 🔧 Development Principles

- **Clean Architecture** - Separation of concerns and dependency inversion
- **Domain-Driven Design** - Rich domain models and ubiquitous language
- **SOLID Principles** - Maintainable and extensible code

## 📞 Support

For questions, issues, or contributions:

1. Check the relevant documentation section
2. Review existing issues in the repository
3. Create a new issue with detailed information
4. Follow the contribution guidelines in [Development Guide](05-development.md)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Next Steps**: Start with [Project Overview](01-project-overview.md) to understand the system in detail.
