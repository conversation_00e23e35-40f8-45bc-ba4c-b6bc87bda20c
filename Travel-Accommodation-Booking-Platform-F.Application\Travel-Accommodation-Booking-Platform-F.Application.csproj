﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <RootNamespace>Travel_Accommodation_Booking_Platform_F.Application</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Travel-Accommodation-Booking-Platform-F.Domain\Travel-Accommodation-Booking-Platform-F.Domain.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Isopoh.Cryptography.Argon2" Version="2.0.0"/>
        <PackageReference Include="AutoMapper" Version="14.0.0"/>
        <PackageReference Include="MediatR" Version="12.2.0"/>
        <PackageReference Include="FluentValidation" Version="11.9.0"/>
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6"/>
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0"/>
        <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    </ItemGroup>

</Project>
