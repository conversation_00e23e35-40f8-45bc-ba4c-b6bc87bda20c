2025-07-26 15:39:52.064 +03:00 [DBG] An 'IServiceProvider' was created for internal use by Entity Framework.
2025-07-26 15:39:52.886 +03:00 [DBG] Entity Framework Core 9.0.0 initialized 'ApplicationDbContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:9.0.0' with options: EngineType=SqlServer 
2025-07-26 15:39:53.067 +03:00 [DBG] Creating DbConnection.
2025-07-26 15:39:53.116 +03:00 [DBG] Created DbConnection. (47ms).
2025-07-26 15:39:53.122 +03:00 [DBG] Migrating using database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:39:53.131 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:39:53.322 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:39:53.331 +03:00 [DBG] Creating DbCommand for 'ExecuteNonQuery'.
2025-07-26 15:39:53.340 +03:00 [DBG] Created DbCommand for 'ExecuteNonQuery' (8ms).
2025-07-26 15:39:53.344 +03:00 [DBG] Initialized DbCommand for 'ExecuteNonQuery' (15ms).
2025-07-26 15:39:53.354 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-26 15:39:53.391 +03:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-26 15:39:53.397 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:39:53.408 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (6ms).
2025-07-26 15:39:53.411 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:39:53.414 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:39:53.416 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-26 15:39:53.419 +03:00 [DBG] Creating DbCommand for 'ExecuteScalar'.
2025-07-26 15:39:53.420 +03:00 [DBG] Created DbCommand for 'ExecuteScalar' (1ms).
2025-07-26 15:39:53.422 +03:00 [DBG] Initialized DbCommand for 'ExecuteScalar' (3ms).
2025-07-26 15:39:53.424 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-07-26 15:39:53.445 +03:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-07-26 15:39:53.517 +03:00 [DBG] Creating DbCommand for 'ExecuteNonQuery'.
2025-07-26 15:39:53.519 +03:00 [DBG] Created DbCommand for 'ExecuteNonQuery' (1ms).
2025-07-26 15:39:53.520 +03:00 [DBG] Initialized DbCommand for 'ExecuteNonQuery' (2ms).
2025-07-26 15:39:53.521 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-07-26 15:39:53.527 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-07-26 15:39:53.533 +03:00 [DBG] Beginning transaction with isolation level 'Unspecified'.
2025-07-26 15:39:53.540 +03:00 [DBG] Began transaction with isolation level 'ReadCommitted'.
2025-07-26 15:39:53.544 +03:00 [DBG] Creating DbCommand for 'ExecuteNonQuery'.
2025-07-26 15:39:53.545 +03:00 [DBG] Created DbCommand for 'ExecuteNonQuery' (0ms).
2025-07-26 15:39:53.548 +03:00 [DBG] Initialized DbCommand for 'ExecuteNonQuery' (4ms).
2025-07-26 15:39:53.550 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-26 15:39:53.552 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-26 15:39:53.555 +03:00 [DBG] Creating DbCommand for 'ExecuteScalar'.
2025-07-26 15:39:53.556 +03:00 [DBG] Created DbCommand for 'ExecuteScalar' (0ms).
2025-07-26 15:39:53.557 +03:00 [DBG] Initialized DbCommand for 'ExecuteScalar' (2ms).
2025-07-26 15:39:53.558 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-26 15:39:53.560 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-26 15:39:53.564 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:39:53.565 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (0ms).
2025-07-26 15:39:53.565 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:39:53.567 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-26 15:39:53.572 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-26 15:39:53.577 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:39:53.579 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 3ms reading results.
2025-07-26 15:39:53.589 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-07-26 15:39:53.592 +03:00 [DBG] Committing transaction.
2025-07-26 15:39:53.596 +03:00 [DBG] Committed transaction.
2025-07-26 15:39:53.598 +03:00 [DBG] Creating DbCommand for 'ExecuteScalar'.
2025-07-26 15:39:53.599 +03:00 [DBG] Created DbCommand for 'ExecuteScalar' (0ms).
2025-07-26 15:39:53.600 +03:00 [DBG] Initialized DbCommand for 'ExecuteScalar' (1ms).
2025-07-26 15:39:53.601 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-07-26 15:39:53.605 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-07-26 15:39:53.607 +03:00 [DBG] Disposing transaction.
2025-07-26 15:39:53.608 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:39:53.610 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:39:53.613 +03:00 [DBG] 'ApplicationDbContext' disposed.
2025-07-26 15:39:53.616 +03:00 [DBG] Disposing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:39:53.618 +03:00 [DBG] Disposed connection to database '' on server '' (1ms).
2025-07-26 15:39:53.671 +03:00 [DBG] Registered model binder providers, in the following order: ["Microsoft.AspNetCore.Mvc.Versioning.ApiVersionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BinderTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ServicesModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.HeaderModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FloatingPointTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.EnumTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.TryParseModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CancellationTokenModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ByteArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormFileModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormCollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.KeyValuePairModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DictionaryModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ComplexObjectModelBinderProvider"]
2025-07-26 15:39:54.028 +03:00 [DBG] Hosting starting
2025-07-26 15:39:54.039 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-26 15:39:54.048 +03:00 [DBG] Reading data from file 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-319f187e-4a98-49f6-8c46-784e4d26bef3.xml'.
2025-07-26 15:39:54.074 +03:00 [DBG] Found key {319f187e-4a98-49f6-8c46-784e4d26bef3}.
2025-07-26 15:39:54.089 +03:00 [DBG] Considering key {319f187e-4a98-49f6-8c46-784e4d26bef3} with expiration date 2025-08-15 09:54:18Z as default key.
2025-07-26 15:39:54.093 +03:00 [DBG] Forwarded activator type request from Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60 to Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60
2025-07-26 15:39:54.095 +03:00 [DBG] Decrypting secret element using Windows DPAPI.
2025-07-26 15:39:54.099 +03:00 [DBG] Forwarded activator type request from Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60 to Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60
2025-07-26 15:39:54.102 +03:00 [DBG] Opening CNG algorithm 'AES' from provider 'null' with chaining mode CBC.
2025-07-26 15:39:54.104 +03:00 [DBG] Opening CNG algorithm 'SHA256' from provider 'null' with HMAC.
2025-07-26 15:39:54.107 +03:00 [DBG] Using key {319f187e-4a98-49f6-8c46-784e4d26bef3} as the default key.
2025-07-26 15:39:54.109 +03:00 [DBG] Key ring with default key {319f187e-4a98-49f6-8c46-784e4d26bef3} was loaded during application startup.
2025-07-26 15:39:54.198 +03:00 [WRN] The WebRootPath was not found: C:\Users\<USER>\RiderProjects\Travel-Accommodation-Booking-Platform-F\Travel-Accommodation-Booking-Platform-F.API\wwwroot. Static files may be unavailable.
2025-07-26 15:39:54.205 +03:00 [DBG] Middleware configuration started with options: {AllowedHosts = *, AllowEmptyHosts = True, IncludeFailureMessage = True}
2025-07-26 15:39:54.208 +03:00 [DBG] Wildcard detected, all requests with hosts will be allowed.
2025-07-26 15:39:54.227 +03:00 [INF] Now listening on: http://localhost:5000
2025-07-26 15:39:54.228 +03:00 [DBG] Loaded hosting startup assembly Travel-Accommodation-Booking-Platform-F.API
2025-07-26 15:39:54.229 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-26 15:39:54.230 +03:00 [INF] Hosting environment: Development
2025-07-26 15:39:54.231 +03:00 [INF] Content root path: C:\Users\<USER>\RiderProjects\Travel-Accommodation-Booking-Platform-F\Travel-Accommodation-Booking-Platform-F.API
2025-07-26 15:39:54.232 +03:00 [DBG] Hosting started
2025-07-26 15:42:39.030 +03:00 [DBG] Connection id "0HNEC87JSNB87" received FIN.
2025-07-26 15:42:39.039 +03:00 [DBG] Connection id "0HNEC87JSNB87" accepted.
2025-07-26 15:42:39.041 +03:00 [DBG] Connection id "0HNEC87JSNB87" started.
2025-07-26 15:42:39.045 +03:00 [DBG] Connection id "0HNEC87JSNB88" accepted.
2025-07-26 15:42:39.046 +03:00 [DBG] Connection id "0HNEC87JSNB88" started.
2025-07-26 15:42:39.053 +03:00 [DBG] Connection id "0HNEC87JSNB87" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-26 15:42:39.057 +03:00 [DBG] Connection id "0HNEC87JSNB87" disconnecting.
2025-07-26 15:42:39.060 +03:00 [DBG] Connection id "0HNEC87JSNB87" stopped.
2025-07-26 15:42:39.085 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - null null
2025-07-26 15:42:39.129 +03:00 [DBG] 1 candidate(s) found for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:42:39.135 +03:00 [DBG] Endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)' with route pattern 'api/v{version:apiVersion}/Admins/top-visited-cities' is valid for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:42:39.141 +03:00 [DBG] Request matched endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:42:39.144 +03:00 [DBG] Static files was skipped as the request already matched an endpoint.
2025-07-26 15:42:39.147 +03:00 [DBG] The request is insecure. Skipping HSTS header.
2025-07-26 15:42:39.205 +03:00 [WRN] Failed to determine the https port for redirect.
2025-07-26 15:42:39.342 +03:00 [DBG] Successfully validated the token.
2025-07-26 15:42:39.348 +03:00 [INF] Checking if token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is blacklisted.
2025-07-26 15:42:39.378 +03:00 [DBG] Entity Framework Core 9.0.0 initialized 'ApplicationDbContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:9.0.0' with options: EngineType=SqlServer 
2025-07-26 15:42:39.496 +03:00 [DBG] Compiling query expression: 
'DbSet<BlacklistedToken>()
    .Any(t => t.Jti == __jti_0 && t.Expiration > DateTime.UtcNow)'
2025-07-26 15:42:39.762 +03:00 [DBG] Generated query execution expression: 
'queryContext => ShapedQueryCompilingExpressionVisitor.SingleAsync<bool>(
    asyncEnumerable: SingleQueryingEnumerable.Create<bool>(
        relationalQueryContext: (RelationalQueryContext)queryContext, 
        relationalCommandResolver: parameters => [LIFTABLE Constant: RelationalCommandCache.QueryExpression(
            Projection Mapping:
                EmptyProjectionMember -> 0
            SELECT EXISTS (
                SELECT 1
                FROM BlacklistedTokens AS b
                WHERE (b.Jti == @__jti_0) && (b.Expiration > GETUTCDATE()))) | Resolver: c => new RelationalCommandCache(
            c.Dependencies.MemoryCache, 
            c.RelationalDependencies.QuerySqlGeneratorFactory, 
            c.RelationalDependencies.RelationalParameterBasedSqlProcessorFactory, 
            Projection Mapping:
                EmptyProjectionMember -> 0
            SELECT EXISTS (
                SELECT 1
                FROM BlacklistedTokens AS b
                WHERE (b.Jti == @__jti_0) && (b.Expiration > GETUTCDATE())), 
            False, 
            new HashSet<string>(
                new string[]{ }, 
                StringComparer.Ordinal
            )
        )].GetRelationalCommandTemplate(parameters), 
        readerColumns: null, 
        shaper: (queryContext, dataReader, resultContext, resultCoordinator) => 
        {
            bool? value1;
            value1 = dataReader.IsDBNull(0) ? default(bool?) : (bool?)dataReader.GetBoolean(0);
            return (bool)value1;
        }, 
        contextType: Travel_Accommodation_Booking_Platform_F.Infrastructure.Persistence.ApplicationDbContext, 
        standAloneStateManager: False, 
        detailedErrorsEnabled: False, 
        threadSafetyChecksEnabled: True), 
    cancellationToken: queryContext.CancellationToken)'
2025-07-26 15:42:39.832 +03:00 [DBG] Creating DbConnection.
2025-07-26 15:42:39.834 +03:00 [DBG] Created DbConnection. (2ms).
2025-07-26 15:42:39.837 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:39.842 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:39.844 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:42:39.845 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:42:39.853 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (9ms).
2025-07-26 15:42:39.858 +03:00 [DBG] Executing DbCommand [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:42:39.903 +03:00 [INF] Executed DbCommand (48ms) [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:42:39.923 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:39.924 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 18ms reading results.
2025-07-26 15:42:39.928 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:39.931 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (2ms).
2025-07-26 15:42:39.933 +03:00 [INF] Token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is not blacklisted.
2025-07-26 15:42:39.936 +03:00 [DBG] AuthenticationScheme: Bearer was successfully authenticated.
2025-07-26 15:42:39.948 +03:00 [DBG] Authorization was successful.
2025-07-26 15:42:39.950 +03:00 [INF] Executing endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:42:39.968 +03:00 [INF] Route matched with {action = "GetTopVisitedCities", controller = "Admins"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTopVisitedCities() on controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API).
2025-07-26 15:42:39.972 +03:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-26 15:42:39.975 +03:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-26 15:42:39.978 +03:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:42:39.980 +03:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-26 15:42:39.982 +03:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:42:39.986 +03:00 [DBG] Executing controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:42:40.137 +03:00 [DBG] Executed controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:42:40.145 +03:00 [INF] Executing action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) - Validation state: "Valid"
2025-07-26 15:42:40.150 +03:00 [INF] Get Top Visited Cities request received
2025-07-26 15:42:40.153 +03:00 [INF] Fetching cities from repository
2025-07-26 15:42:40.166 +03:00 [DBG] Compiling query expression: 
'DbSet<Booking>()
    .GroupBy(b => b.Room.Hotel.CityId)
    .Select(g => new { 
        CityId = g.Key, 
        Count = g
            .Count()
     })
    .OrderByDescending(g => g.Count)
    .Take(__p_0)
    .Select(g => g.CityId)'
2025-07-26 15:42:40.310 +03:00 [DBG] Generated query execution expression: 
'queryContext => SingleQueryingEnumerable.Create<int>(
    relationalQueryContext: (RelationalQueryContext)queryContext, 
    relationalCommandResolver: parameters => [LIFTABLE Constant: RelationalCommandCache.QueryExpression(
        Projection Mapping:
            EmptyProjectionMember -> 0
        SELECT TOP(@__p_0) h.CityId
        FROM Bookings AS b
        INNER JOIN Rooms AS r ON b.RoomId == r.RoomId
        INNER JOIN Hotels AS h ON r.HotelId == h.HotelId
        GROUP BY h.CityId
        ORDER BY COUNT(*) DESC) | Resolver: c => new RelationalCommandCache(
        c.Dependencies.MemoryCache, 
        c.RelationalDependencies.QuerySqlGeneratorFactory, 
        c.RelationalDependencies.RelationalParameterBasedSqlProcessorFactory, 
        Projection Mapping:
            EmptyProjectionMember -> 0
        SELECT TOP(@__p_0) h.CityId
        FROM Bookings AS b
        INNER JOIN Rooms AS r ON b.RoomId == r.RoomId
        INNER JOIN Hotels AS h ON r.HotelId == h.HotelId
        GROUP BY h.CityId
        ORDER BY COUNT(*) DESC, 
        False, 
        new HashSet<string>(
            new string[]{ }, 
            StringComparer.Ordinal
        )
    )].GetRelationalCommandTemplate(parameters), 
    readerColumns: null, 
    shaper: (queryContext, dataReader, resultContext, resultCoordinator) => 
    {
        int? value1;
        value1 = (int?)dataReader.GetInt32(0);
        return (int)value1;
    }, 
    contextType: Travel_Accommodation_Booking_Platform_F.Infrastructure.Persistence.ApplicationDbContext, 
    standAloneStateManager: False, 
    detailedErrorsEnabled: False, 
    threadSafetyChecksEnabled: True)'
2025-07-26 15:42:40.327 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:40.328 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:40.330 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:42:40.332 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:42:40.333 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (3ms).
2025-07-26 15:42:40.336 +03:00 [DBG] Executing DbCommand [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:42:40.353 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:42:40.361 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:40.363 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 6ms reading results.
2025-07-26 15:42:40.365 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:40.367 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (2ms).
2025-07-26 15:42:40.377 +03:00 [DBG] Compiling query expression: 
'DbSet<City>()
    .Where(c => __topCityIds_0.Contains(c.CityId))
    .AsNoTracking()'
2025-07-26 15:42:40.502 +03:00 [DBG] Generated query execution expression: 
'queryContext => SingleQueryingEnumerable.Create<City>(
    relationalQueryContext: (RelationalQueryContext)queryContext, 
    relationalCommandResolver: parameters => [LIFTABLE Constant: RelationalCommandCache.QueryExpression(
        Projection Mapping:
            EmptyProjectionMember -> Dictionary<IProperty, int> { [Property: City.CityId (int) Required PK AfterSave:Throw ValueGenerated.OnAdd, 0], [Property: City.Country (string) Required, 1], [Property: City.CreatedAt (DateTime) Required, 2], [Property: City.LastUpdated (DateTime) Required, 3], [Property: City.Name (string) Required, 4], [Property: City.NumberOfHotels (int) Required, 5], [Property: City.PostOffice (string) Required, 6], [Property: City.UpdatedAt (DateTime) Required, 7] }
        SELECT c.CityId, c.Country, c.CreatedAt, c.LastUpdated, c.Name, c.NumberOfHotels, c.PostOffice, c.UpdatedAt
        FROM Cities AS c
        WHERE c.CityId IN (
            SELECT t.value
            FROM OPENJSON(@__topCityIds_0) WITH (value int '') AS t)) | Resolver: c => new RelationalCommandCache(
        c.Dependencies.MemoryCache, 
        c.RelationalDependencies.QuerySqlGeneratorFactory, 
        c.RelationalDependencies.RelationalParameterBasedSqlProcessorFactory, 
        Projection Mapping:
            EmptyProjectionMember -> Dictionary<IProperty, int> { [Property: City.CityId (int) Required PK AfterSave:Throw ValueGenerated.OnAdd, 0], [Property: City.Country (string) Required, 1], [Property: City.CreatedAt (DateTime) Required, 2], [Property: City.LastUpdated (DateTime) Required, 3], [Property: City.Name (string) Required, 4], [Property: City.NumberOfHotels (int) Required, 5], [Property: City.PostOffice (string) Required, 6], [Property: City.UpdatedAt (DateTime) Required, 7] }
        SELECT c.CityId, c.Country, c.CreatedAt, c.LastUpdated, c.Name, c.NumberOfHotels, c.PostOffice, c.UpdatedAt
        FROM Cities AS c
        WHERE c.CityId IN (
            SELECT t.value
            FROM OPENJSON(@__topCityIds_0) WITH (value int '') AS t), 
        False, 
        new HashSet<string>(
            new string[]{ }, 
            StringComparer.Ordinal
        )
    )].GetRelationalCommandTemplate(parameters), 
    readerColumns: null, 
    shaper: (queryContext, dataReader, resultContext, resultCoordinator) => 
    {
        City entity;
        entity = 
        {
            MaterializationContext materializationContext1;
            IEntityType entityType1;
            City instance1;
            materializationContext1 = new MaterializationContext(
                [LIFTABLE Constant: ValueBuffer | Resolver: _ => (object)ValueBuffer.Empty], 
                queryContext.Context
            );
            instance1 = default(City);
            (object)dataReader.GetInt32(0) != null ? 
            {
                ISnapshot shadowSnapshot1;
                shadowSnapshot1 = [LIFTABLE Constant: Snapshot | Resolver: _ => Snapshot.Empty];
                entityType1 = [LIFTABLE Constant: EntityType: City | Resolver: namelessParameter{0} => namelessParameter{0}.Dependencies.Model.FindEntityType("Travel_Accommodation_Booking_Platform_F.Domain.Entities.City")];
                instance1 = switch (entityType1)
                {
                    case [LIFTABLE Constant: EntityType: City | Resolver: namelessParameter{1} => namelessParameter{1}.Dependencies.Model.FindEntityType("Travel_Accommodation_Booking_Platform_F.Domain.Entities.City")]: 
                        {
                            return 
                            {
                                City instance;
                                instance = new City();
                                instance.<CityId>k__BackingField = dataReader.GetInt32(0);
                                instance.<Country>k__BackingField = dataReader.GetString(1);
                                instance.<CreatedAt>k__BackingField = dataReader.GetDateTime(2);
                                instance.<LastUpdated>k__BackingField = dataReader.GetDateTime(3);
                                instance.<Name>k__BackingField = dataReader.GetString(4);
                                instance.<NumberOfHotels>k__BackingField = dataReader.GetInt32(5);
                                instance.<PostOffice>k__BackingField = dataReader.GetString(6);
                                instance.<UpdatedAt>k__BackingField = dataReader.GetDateTime(7);
                                (instance is IInjectableService) ? ((IInjectableService)instance).Injected(
                                    context: materializationContext1.Context, 
                                    entity: instance, 
                                    queryTrackingBehavior: NoTracking, 
                                    structuralType: [LIFTABLE Constant: EntityType: City | Resolver: namelessParameter{2} => namelessParameter{2}.Dependencies.Model.FindEntityType("Travel_Accommodation_Booking_Platform_F.Domain.Entities.City")]) : default(void);
                                return instance;
                            }}
                    default: 
                        default(City)
                }
                ;
                return instance1;
            } : 
            {
                object[] keyValues1;
                keyValues1 = new object[]{ (object)dataReader.GetInt32(0) };
                return ShapedQueryCompilingExpressionVisitor.CreateNullKeyValueInNoTrackingQuery(
                    entityType: [LIFTABLE Constant: EntityType: City | Resolver: namelessParameter{3} => namelessParameter{3}.Dependencies.Model.FindEntityType("Travel_Accommodation_Booking_Platform_F.Domain.Entities.City")], 
                    properties: [LIFTABLE Constant: List<RuntimeProperty> { Property: City.CityId (int) Required PK AfterSave:Throw ValueGenerated.OnAdd } | Resolver: c => c.Dependencies.Model.FindEntityType("Travel_Accommodation_Booking_Platform_F.Domain.Entities.City").FindPrimaryKey().Properties], 
                    keyValues: keyValues1);
            };
            return instance1;
        };
        return entity;
    }, 
    contextType: Travel_Accommodation_Booking_Platform_F.Infrastructure.Persistence.ApplicationDbContext, 
    standAloneStateManager: False, 
    detailedErrorsEnabled: False, 
    threadSafetyChecksEnabled: True)'
2025-07-26 15:42:40.521 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:40.523 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:40.525 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:42:40.526 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:42:40.536 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (11ms).
2025-07-26 15:42:40.538 +03:00 [DBG] Executing DbCommand [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:42:40.556 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:42:40.565 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:40.566 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 6ms reading results.
2025-07-26 15:42:40.568 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:40.570 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (2ms).
2025-07-26 15:42:40.573 +03:00 [INF] Fetched cities from repository successfully
2025-07-26 15:42:40.596 +03:00 [INF] Check if list of top visited cities not updated
2025-07-26 15:42:40.598 +03:00 [INF] Send ETag to client if list of top visited cities updated recently
2025-07-26 15:42:40.599 +03:00 [INF] Get Top Visited Cities success
2025-07-26 15:42:40.608 +03:00 [INF] Executed action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 455.5459ms.
2025-07-26 15:42:40.623 +03:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-26 15:42:40.629 +03:00 [DBG] No information found on request to perform content negotiation.
2025-07-26 15:42:40.631 +03:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-26 15:42:40.632 +03:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-26 15:42:40.635 +03:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-26 15:42:40.637 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Travel_Accommodation_Booking_Platform_F.Application.DTOs.ReadDTOs.CityReadDto, Travel-Accommodation-Booking-Platform-F.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-07-26 15:42:40.710 +03:00 [INF] Executed action Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) in 719.5917ms
2025-07-26 15:42:40.713 +03:00 [INF] Executed endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:42:40.719 +03:00 [DBG] Connection id "0HNEC87JSNB88" completed keep alive response.
2025-07-26 15:42:40.724 +03:00 [DBG] 'ApplicationDbContext' disposed.
2025-07-26 15:42:40.732 +03:00 [DBG] Disposing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:40.734 +03:00 [DBG] Disposed connection to database '' on server '' (1ms).
2025-07-26 15:42:40.737 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - 200 null application/json; charset=utf-8 1654.232ms
2025-07-26 15:42:58.579 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - null null
2025-07-26 15:42:58.585 +03:00 [DBG] 1 candidate(s) found for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:42:58.586 +03:00 [DBG] Endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)' with route pattern 'api/v{version:apiVersion}/Admins/top-visited-cities' is valid for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:42:58.588 +03:00 [DBG] Request matched endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:42:58.589 +03:00 [DBG] Static files was skipped as the request already matched an endpoint.
2025-07-26 15:42:58.590 +03:00 [DBG] The request is insecure. Skipping HSTS header.
2025-07-26 15:42:58.593 +03:00 [DBG] Successfully validated the token.
2025-07-26 15:42:58.595 +03:00 [INF] Checking if token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is blacklisted.
2025-07-26 15:42:58.640 +03:00 [DBG] Entity Framework Core 9.0.0 initialized 'ApplicationDbContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:9.0.0' with options: EngineType=SqlServer 
2025-07-26 15:42:58.647 +03:00 [DBG] Creating DbConnection.
2025-07-26 15:42:58.648 +03:00 [DBG] Created DbConnection. (1ms).
2025-07-26 15:42:58.649 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:58.650 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:58.651 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:42:58.652 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (0ms).
2025-07-26 15:42:58.653 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:42:58.654 +03:00 [DBG] Executing DbCommand [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:42:58.657 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:42:58.659 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:58.660 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:42:58.662 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:58.663 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:42:58.664 +03:00 [INF] Token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is not blacklisted.
2025-07-26 15:42:58.665 +03:00 [DBG] AuthenticationScheme: Bearer was successfully authenticated.
2025-07-26 15:42:58.668 +03:00 [DBG] Authorization was successful.
2025-07-26 15:42:58.669 +03:00 [INF] Executing endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:42:58.670 +03:00 [INF] Route matched with {action = "GetTopVisitedCities", controller = "Admins"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTopVisitedCities() on controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API).
2025-07-26 15:42:58.672 +03:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-26 15:42:58.673 +03:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-26 15:42:58.675 +03:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:42:58.677 +03:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-26 15:42:58.678 +03:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:42:58.679 +03:00 [DBG] Executing controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:42:58.681 +03:00 [DBG] Executed controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:42:58.682 +03:00 [INF] Executing action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) - Validation state: "Valid"
2025-07-26 15:42:58.683 +03:00 [INF] Get Top Visited Cities request received
2025-07-26 15:42:58.684 +03:00 [INF] Fetching cities from repository
2025-07-26 15:42:58.686 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:58.687 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:58.688 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:42:58.689 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:42:58.690 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:42:58.692 +03:00 [DBG] Executing DbCommand [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:42:58.694 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:42:58.697 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:58.698 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:42:58.699 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:58.700 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:42:58.702 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:58.703 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:58.705 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:42:58.705 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (0ms).
2025-07-26 15:42:58.707 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:42:58.708 +03:00 [DBG] Executing DbCommand [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:42:58.710 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:42:58.712 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:58.713 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:42:58.715 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:58.716 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:42:58.717 +03:00 [INF] Fetched cities from repository successfully
2025-07-26 15:42:58.719 +03:00 [INF] Check if list of top visited cities not updated
2025-07-26 15:42:58.720 +03:00 [INF] Send ETag to client if list of top visited cities updated recently
2025-07-26 15:42:58.720 +03:00 [INF] Get Top Visited Cities success
2025-07-26 15:42:58.721 +03:00 [INF] Executed action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 37.9301ms.
2025-07-26 15:42:58.723 +03:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-26 15:42:58.725 +03:00 [DBG] No information found on request to perform content negotiation.
2025-07-26 15:42:58.725 +03:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-26 15:42:58.726 +03:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-26 15:42:58.727 +03:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-26 15:42:58.728 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Travel_Accommodation_Booking_Platform_F.Application.DTOs.ReadDTOs.CityReadDto, Travel-Accommodation-Booking-Platform-F.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-07-26 15:42:58.729 +03:00 [INF] Executed action Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) in 49.6132ms
2025-07-26 15:42:58.730 +03:00 [INF] Executed endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:42:58.731 +03:00 [DBG] Connection id "0HNEC87JSNB88" completed keep alive response.
2025-07-26 15:42:58.732 +03:00 [DBG] 'ApplicationDbContext' disposed.
2025-07-26 15:42:58.733 +03:00 [DBG] Disposing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:42:58.735 +03:00 [DBG] Disposed connection to database '' on server '' (1ms).
2025-07-26 15:42:58.736 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - 200 null application/json; charset=utf-8 157.3544ms
2025-07-26 15:43:15.197 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - null null
2025-07-26 15:43:15.201 +03:00 [DBG] 1 candidate(s) found for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:43:15.203 +03:00 [DBG] Endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)' with route pattern 'api/v{version:apiVersion}/Admins/top-visited-cities' is valid for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:43:15.204 +03:00 [DBG] Request matched endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:15.206 +03:00 [DBG] Static files was skipped as the request already matched an endpoint.
2025-07-26 15:43:15.208 +03:00 [DBG] The request is insecure. Skipping HSTS header.
2025-07-26 15:43:15.211 +03:00 [DBG] Successfully validated the token.
2025-07-26 15:43:15.216 +03:00 [INF] Checking if token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is blacklisted.
2025-07-26 15:43:15.218 +03:00 [DBG] Entity Framework Core 9.0.0 initialized 'ApplicationDbContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:9.0.0' with options: EngineType=SqlServer 
2025-07-26 15:43:15.222 +03:00 [DBG] Creating DbConnection.
2025-07-26 15:43:15.222 +03:00 [DBG] Created DbConnection. (0ms).
2025-07-26 15:43:15.223 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:15.225 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:15.226 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:15.228 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:43:15.230 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (3ms).
2025-07-26 15:43:15.231 +03:00 [DBG] Executing DbCommand [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:43:15.235 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:43:15.237 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:15.238 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:43:15.240 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:15.241 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:43:15.244 +03:00 [INF] Token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is not blacklisted.
2025-07-26 15:43:15.245 +03:00 [DBG] AuthenticationScheme: Bearer was successfully authenticated.
2025-07-26 15:43:15.248 +03:00 [DBG] Authorization was successful.
2025-07-26 15:43:15.249 +03:00 [INF] Executing endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:15.250 +03:00 [INF] Route matched with {action = "GetTopVisitedCities", controller = "Admins"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTopVisitedCities() on controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API).
2025-07-26 15:43:15.252 +03:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-26 15:43:15.253 +03:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-26 15:43:15.254 +03:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:43:15.256 +03:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-26 15:43:15.257 +03:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:43:15.260 +03:00 [DBG] Executing controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:43:15.263 +03:00 [DBG] Executed controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:43:15.264 +03:00 [INF] Executing action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) - Validation state: "Valid"
2025-07-26 15:43:15.265 +03:00 [INF] Get Top Visited Cities request received
2025-07-26 15:43:15.266 +03:00 [INF] Fetching cities from repository
2025-07-26 15:43:15.267 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:15.269 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:15.270 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:15.271 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (0ms).
2025-07-26 15:43:15.272 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:43:15.274 +03:00 [DBG] Executing DbCommand [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:43:15.278 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:43:15.280 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:15.281 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:43:15.283 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:15.284 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:43:15.286 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:15.287 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:15.288 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:15.290 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:43:15.292 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (4ms).
2025-07-26 15:43:15.294 +03:00 [DBG] Executing DbCommand [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:43:15.298 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:43:15.300 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:15.301 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:43:15.303 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:15.305 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (2ms).
2025-07-26 15:43:15.307 +03:00 [INF] Fetched cities from repository successfully
2025-07-26 15:43:15.308 +03:00 [INF] Check if list of top visited cities not updated
2025-07-26 15:43:15.309 +03:00 [INF] Send ETag to client if list of top visited cities updated recently
2025-07-26 15:43:15.310 +03:00 [INF] Get Top Visited Cities success
2025-07-26 15:43:15.311 +03:00 [INF] Executed action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 45.3742ms.
2025-07-26 15:43:15.313 +03:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-26 15:43:15.315 +03:00 [DBG] No information found on request to perform content negotiation.
2025-07-26 15:43:15.315 +03:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-26 15:43:15.316 +03:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-26 15:43:15.317 +03:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-26 15:43:15.319 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Travel_Accommodation_Booking_Platform_F.Application.DTOs.ReadDTOs.CityReadDto, Travel-Accommodation-Booking-Platform-F.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-07-26 15:43:15.320 +03:00 [INF] Executed action Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) in 60.7402ms
2025-07-26 15:43:15.322 +03:00 [INF] Executed endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:15.324 +03:00 [DBG] Connection id "0HNEC87JSNB88" completed keep alive response.
2025-07-26 15:43:15.325 +03:00 [DBG] 'ApplicationDbContext' disposed.
2025-07-26 15:43:15.326 +03:00 [DBG] Disposing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:15.327 +03:00 [DBG] Disposed connection to database '' on server '' (1ms).
2025-07-26 15:43:15.329 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - 200 null application/json; charset=utf-8 131.2673ms
2025-07-26 15:43:16.530 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - null null
2025-07-26 15:43:16.533 +03:00 [DBG] 1 candidate(s) found for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:43:16.535 +03:00 [DBG] Endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)' with route pattern 'api/v{version:apiVersion}/Admins/top-visited-cities' is valid for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:43:16.537 +03:00 [DBG] Request matched endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:16.538 +03:00 [DBG] Static files was skipped as the request already matched an endpoint.
2025-07-26 15:43:16.539 +03:00 [DBG] The request is insecure. Skipping HSTS header.
2025-07-26 15:43:16.541 +03:00 [DBG] Successfully validated the token.
2025-07-26 15:43:16.542 +03:00 [INF] Checking if token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is blacklisted.
2025-07-26 15:43:16.543 +03:00 [DBG] Entity Framework Core 9.0.0 initialized 'ApplicationDbContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:9.0.0' with options: EngineType=SqlServer 
2025-07-26 15:43:16.546 +03:00 [DBG] Creating DbConnection.
2025-07-26 15:43:16.547 +03:00 [DBG] Created DbConnection. (1ms).
2025-07-26 15:43:16.548 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:16.550 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:16.552 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:16.553 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:43:16.554 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:43:16.557 +03:00 [DBG] Executing DbCommand [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:43:16.559 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:43:16.561 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:16.563 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:43:16.565 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:16.568 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (2ms).
2025-07-26 15:43:16.570 +03:00 [INF] Token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is not blacklisted.
2025-07-26 15:43:16.571 +03:00 [DBG] AuthenticationScheme: Bearer was successfully authenticated.
2025-07-26 15:43:16.572 +03:00 [DBG] Authorization was successful.
2025-07-26 15:43:16.573 +03:00 [INF] Executing endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:16.574 +03:00 [INF] Route matched with {action = "GetTopVisitedCities", controller = "Admins"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTopVisitedCities() on controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API).
2025-07-26 15:43:16.576 +03:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-26 15:43:16.578 +03:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-26 15:43:16.579 +03:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:43:16.582 +03:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-26 15:43:16.584 +03:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:43:16.586 +03:00 [DBG] Executing controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:43:16.588 +03:00 [DBG] Executed controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:43:16.589 +03:00 [INF] Executing action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) - Validation state: "Valid"
2025-07-26 15:43:16.590 +03:00 [INF] Get Top Visited Cities request received
2025-07-26 15:43:16.591 +03:00 [INF] Fetching cities from repository
2025-07-26 15:43:16.592 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:16.593 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:16.595 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:16.597 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:43:16.598 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (3ms).
2025-07-26 15:43:16.599 +03:00 [DBG] Executing DbCommand [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:43:16.602 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:43:16.605 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:16.606 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:43:16.607 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:16.609 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:43:16.611 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:16.613 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:16.614 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:16.615 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (0ms).
2025-07-26 15:43:16.616 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:43:16.618 +03:00 [DBG] Executing DbCommand [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:43:16.621 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:43:16.623 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:16.625 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:43:16.626 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:16.628 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:43:16.630 +03:00 [INF] Fetched cities from repository successfully
2025-07-26 15:43:16.631 +03:00 [INF] Check if list of top visited cities not updated
2025-07-26 15:43:16.632 +03:00 [INF] Send ETag to client if list of top visited cities updated recently
2025-07-26 15:43:16.633 +03:00 [INF] Get Top Visited Cities success
2025-07-26 15:43:16.633 +03:00 [INF] Executed action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 43.0764ms.
2025-07-26 15:43:16.635 +03:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-26 15:43:16.637 +03:00 [DBG] No information found on request to perform content negotiation.
2025-07-26 15:43:16.638 +03:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-26 15:43:16.639 +03:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-26 15:43:16.640 +03:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-26 15:43:16.641 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Travel_Accommodation_Booking_Platform_F.Application.DTOs.ReadDTOs.CityReadDto, Travel-Accommodation-Booking-Platform-F.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-07-26 15:43:16.643 +03:00 [INF] Executed action Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) in 56.8403ms
2025-07-26 15:43:16.645 +03:00 [INF] Executed endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:16.646 +03:00 [DBG] Connection id "0HNEC87JSNB88" completed keep alive response.
2025-07-26 15:43:16.647 +03:00 [DBG] 'ApplicationDbContext' disposed.
2025-07-26 15:43:16.648 +03:00 [DBG] Disposing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:16.649 +03:00 [DBG] Disposed connection to database '' on server '' (1ms).
2025-07-26 15:43:16.651 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - 200 null application/json; charset=utf-8 120.5165ms
2025-07-26 15:43:17.602 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - null null
2025-07-26 15:43:17.604 +03:00 [DBG] 1 candidate(s) found for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:43:17.606 +03:00 [DBG] Endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)' with route pattern 'api/v{version:apiVersion}/Admins/top-visited-cities' is valid for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:43:17.608 +03:00 [DBG] Request matched endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:17.610 +03:00 [DBG] Static files was skipped as the request already matched an endpoint.
2025-07-26 15:43:17.611 +03:00 [DBG] The request is insecure. Skipping HSTS header.
2025-07-26 15:43:17.613 +03:00 [DBG] Successfully validated the token.
2025-07-26 15:43:17.614 +03:00 [INF] Checking if token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is blacklisted.
2025-07-26 15:43:17.615 +03:00 [DBG] Entity Framework Core 9.0.0 initialized 'ApplicationDbContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:9.0.0' with options: EngineType=SqlServer 
2025-07-26 15:43:17.618 +03:00 [DBG] Creating DbConnection.
2025-07-26 15:43:17.619 +03:00 [DBG] Created DbConnection. (0ms).
2025-07-26 15:43:17.620 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:17.621 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:17.623 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:17.625 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:43:17.626 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (3ms).
2025-07-26 15:43:17.628 +03:00 [DBG] Executing DbCommand [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:43:17.631 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:43:17.633 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:17.634 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:43:17.635 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:17.637 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:43:17.639 +03:00 [INF] Token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is not blacklisted.
2025-07-26 15:43:17.640 +03:00 [DBG] AuthenticationScheme: Bearer was successfully authenticated.
2025-07-26 15:43:17.641 +03:00 [DBG] Authorization was successful.
2025-07-26 15:43:17.642 +03:00 [INF] Executing endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:17.643 +03:00 [INF] Route matched with {action = "GetTopVisitedCities", controller = "Admins"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTopVisitedCities() on controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API).
2025-07-26 15:43:17.645 +03:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-26 15:43:17.647 +03:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-26 15:43:17.648 +03:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:43:17.650 +03:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-26 15:43:17.651 +03:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:43:17.653 +03:00 [DBG] Executing controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:43:17.654 +03:00 [DBG] Executed controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:43:17.656 +03:00 [INF] Executing action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) - Validation state: "Valid"
2025-07-26 15:43:17.658 +03:00 [INF] Get Top Visited Cities request received
2025-07-26 15:43:17.659 +03:00 [INF] Fetching cities from repository
2025-07-26 15:43:17.660 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:17.661 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:17.662 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:17.663 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:43:17.665 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:43:17.666 +03:00 [DBG] Executing DbCommand [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:43:17.669 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:43:17.671 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:17.673 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 2ms reading results.
2025-07-26 15:43:17.675 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:17.677 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:43:17.679 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:17.680 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:17.681 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:17.682 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:43:17.684 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:43:17.685 +03:00 [DBG] Executing DbCommand [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:43:17.690 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:43:17.692 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:17.693 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:43:17.695 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:17.697 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:43:17.698 +03:00 [INF] Fetched cities from repository successfully
2025-07-26 15:43:17.699 +03:00 [INF] Check if list of top visited cities not updated
2025-07-26 15:43:17.700 +03:00 [INF] Send ETag to client if list of top visited cities updated recently
2025-07-26 15:43:17.701 +03:00 [INF] Get Top Visited Cities success
2025-07-26 15:43:17.702 +03:00 [INF] Executed action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 44.9244ms.
2025-07-26 15:43:17.705 +03:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-26 15:43:17.707 +03:00 [DBG] No information found on request to perform content negotiation.
2025-07-26 15:43:17.708 +03:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-26 15:43:17.709 +03:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-26 15:43:17.710 +03:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-26 15:43:17.711 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Travel_Accommodation_Booking_Platform_F.Application.DTOs.ReadDTOs.CityReadDto, Travel-Accommodation-Booking-Platform-F.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-07-26 15:43:17.713 +03:00 [INF] Executed action Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) in 59.789ms
2025-07-26 15:43:17.714 +03:00 [INF] Executed endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:17.715 +03:00 [DBG] Connection id "0HNEC87JSNB88" completed keep alive response.
2025-07-26 15:43:17.717 +03:00 [DBG] 'ApplicationDbContext' disposed.
2025-07-26 15:43:17.718 +03:00 [DBG] Disposing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:17.720 +03:00 [DBG] Disposed connection to database '' on server '' (1ms).
2025-07-26 15:43:17.722 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - 200 null application/json; charset=utf-8 119.6889ms
2025-07-26 15:43:18.741 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - null null
2025-07-26 15:43:18.743 +03:00 [DBG] 1 candidate(s) found for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:43:18.745 +03:00 [DBG] Endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)' with route pattern 'api/v{version:apiVersion}/Admins/top-visited-cities' is valid for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:43:18.746 +03:00 [DBG] Request matched endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:18.748 +03:00 [DBG] Static files was skipped as the request already matched an endpoint.
2025-07-26 15:43:18.749 +03:00 [DBG] The request is insecure. Skipping HSTS header.
2025-07-26 15:43:18.751 +03:00 [DBG] Successfully validated the token.
2025-07-26 15:43:18.752 +03:00 [INF] Checking if token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is blacklisted.
2025-07-26 15:43:18.754 +03:00 [DBG] Entity Framework Core 9.0.0 initialized 'ApplicationDbContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:9.0.0' with options: EngineType=SqlServer 
2025-07-26 15:43:18.756 +03:00 [DBG] Creating DbConnection.
2025-07-26 15:43:18.757 +03:00 [DBG] Created DbConnection. (0ms).
2025-07-26 15:43:18.758 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:18.759 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:18.760 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:18.761 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (0ms).
2025-07-26 15:43:18.763 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:43:18.765 +03:00 [DBG] Executing DbCommand [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:43:18.768 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:43:18.770 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:18.772 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:43:18.773 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:18.774 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:43:18.776 +03:00 [INF] Token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is not blacklisted.
2025-07-26 15:43:18.778 +03:00 [DBG] AuthenticationScheme: Bearer was successfully authenticated.
2025-07-26 15:43:18.780 +03:00 [DBG] Authorization was successful.
2025-07-26 15:43:18.781 +03:00 [INF] Executing endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:18.782 +03:00 [INF] Route matched with {action = "GetTopVisitedCities", controller = "Admins"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTopVisitedCities() on controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API).
2025-07-26 15:43:18.784 +03:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-26 15:43:18.786 +03:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-26 15:43:18.787 +03:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:43:18.789 +03:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-26 15:43:18.791 +03:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:43:18.793 +03:00 [DBG] Executing controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:43:18.795 +03:00 [DBG] Executed controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:43:18.797 +03:00 [INF] Executing action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) - Validation state: "Valid"
2025-07-26 15:43:18.798 +03:00 [INF] Get Top Visited Cities request received
2025-07-26 15:43:18.799 +03:00 [INF] Fetching cities from repository
2025-07-26 15:43:18.800 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:18.802 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:18.803 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:18.804 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:43:18.806 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:43:18.807 +03:00 [DBG] Executing DbCommand [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:43:18.811 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:43:18.813 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:18.815 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:43:18.816 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:18.818 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:43:18.820 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:18.821 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:18.823 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:18.824 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:43:18.826 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (3ms).
2025-07-26 15:43:18.828 +03:00 [DBG] Executing DbCommand [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:43:18.831 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:43:18.833 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:18.835 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:43:18.836 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:18.838 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:43:18.841 +03:00 [INF] Fetched cities from repository successfully
2025-07-26 15:43:18.842 +03:00 [INF] Check if list of top visited cities not updated
2025-07-26 15:43:18.844 +03:00 [INF] Send ETag to client if list of top visited cities updated recently
2025-07-26 15:43:18.845 +03:00 [INF] Get Top Visited Cities success
2025-07-26 15:43:18.846 +03:00 [INF] Executed action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 47.2871ms.
2025-07-26 15:43:18.848 +03:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-26 15:43:18.850 +03:00 [DBG] No information found on request to perform content negotiation.
2025-07-26 15:43:18.851 +03:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-26 15:43:18.852 +03:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-26 15:43:18.852 +03:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-26 15:43:18.854 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Travel_Accommodation_Booking_Platform_F.Application.DTOs.ReadDTOs.CityReadDto, Travel-Accommodation-Booking-Platform-F.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-07-26 15:43:18.856 +03:00 [INF] Executed action Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) in 62.4878ms
2025-07-26 15:43:18.857 +03:00 [INF] Executed endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:18.858 +03:00 [DBG] Connection id "0HNEC87JSNB88" completed keep alive response.
2025-07-26 15:43:18.860 +03:00 [DBG] 'ApplicationDbContext' disposed.
2025-07-26 15:43:18.861 +03:00 [DBG] Disposing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:18.862 +03:00 [DBG] Disposed connection to database '' on server '' (1ms).
2025-07-26 15:43:18.864 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - 200 null application/json; charset=utf-8 122.5773ms
2025-07-26 15:43:52.924 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/v1/cities - application/json 115
2025-07-26 15:43:52.926 +03:00 [DBG] 1 candidate(s) found for the request path '/api/v1/cities'
2025-07-26 15:43:52.927 +03:00 [DBG] Endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.CitiesController.CreateCity (Travel-Accommodation-Booking-Platform-F.API)' with route pattern 'api/v{version:apiVersion}/Cities' is valid for the request path '/api/v1/cities'
2025-07-26 15:43:52.929 +03:00 [DBG] Request matched endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.CitiesController.CreateCity (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:52.930 +03:00 [DBG] Static files was skipped as the request already matched an endpoint.
2025-07-26 15:43:52.931 +03:00 [DBG] The request is insecure. Skipping HSTS header.
2025-07-26 15:43:52.932 +03:00 [DBG] Successfully validated the token.
2025-07-26 15:43:52.932 +03:00 [INF] Checking if token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is blacklisted.
2025-07-26 15:43:52.934 +03:00 [DBG] Entity Framework Core 9.0.0 initialized 'ApplicationDbContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:9.0.0' with options: EngineType=SqlServer 
2025-07-26 15:43:52.935 +03:00 [DBG] Creating DbConnection.
2025-07-26 15:43:52.936 +03:00 [DBG] Created DbConnection. (0ms).
2025-07-26 15:43:52.937 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:52.939 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:52.940 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:52.940 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (0ms).
2025-07-26 15:43:52.941 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:43:52.942 +03:00 [DBG] Executing DbCommand [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:43:52.947 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:43:52.949 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:52.950 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:43:52.951 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:52.952 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:43:52.954 +03:00 [INF] Token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is not blacklisted.
2025-07-26 15:43:52.954 +03:00 [DBG] AuthenticationScheme: Bearer was successfully authenticated.
2025-07-26 15:43:52.956 +03:00 [DBG] Authorization was successful.
2025-07-26 15:43:52.956 +03:00 [INF] Executing endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.CitiesController.CreateCity (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:52.975 +03:00 [INF] Route matched with {action = "CreateCity", controller = "Cities"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] CreateCity(Travel_Accommodation_Booking_Platform_F.Application.DTOs.WriteDTOs.CityWriteDto) on controller Travel_Accommodation_Booking_Platform_F.Controllers.CitiesController (Travel-Accommodation-Booking-Platform-F.API).
2025-07-26 15:43:52.977 +03:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-26 15:43:52.978 +03:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-26 15:43:52.979 +03:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:43:52.981 +03:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-26 15:43:52.982 +03:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:43:52.984 +03:00 [DBG] Executing controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.CitiesController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:43:52.986 +03:00 [DBG] Executed controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.CitiesController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:43:52.992 +03:00 [DBG] Attempting to bind parameter 'dto' of type 'Travel_Accommodation_Booking_Platform_F.Application.DTOs.WriteDTOs.CityWriteDto' ...
2025-07-26 15:43:52.995 +03:00 [DBG] Attempting to bind parameter 'dto' of type 'Travel_Accommodation_Booking_Platform_F.Application.DTOs.WriteDTOs.CityWriteDto' using the name '' in request data ...
2025-07-26 15:43:52.997 +03:00 [DBG] Selected input formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonInputFormatter' for content type 'application/json'.
2025-07-26 15:43:53.005 +03:00 [DBG] Connection id "0HNEC87JSNB88", Request id "0HNEC87JSNB88:00000007": started reading request body.
2025-07-26 15:43:53.007 +03:00 [DBG] Connection id "0HNEC87JSNB88", Request id "0HNEC87JSNB88:00000007": done reading request body.
2025-07-26 15:43:53.022 +03:00 [DBG] JSON input formatter succeeded, deserializing to type 'Travel_Accommodation_Booking_Platform_F.Application.DTOs.WriteDTOs.CityWriteDto'
2025-07-26 15:43:53.024 +03:00 [DBG] Done attempting to bind parameter 'dto' of type 'Travel_Accommodation_Booking_Platform_F.Application.DTOs.WriteDTOs.CityWriteDto'.
2025-07-26 15:43:53.026 +03:00 [DBG] Done attempting to bind parameter 'dto' of type 'Travel_Accommodation_Booking_Platform_F.Application.DTOs.WriteDTOs.CityWriteDto'.
2025-07-26 15:43:53.028 +03:00 [DBG] Attempting to validate the bound parameter 'dto' of type 'Travel_Accommodation_Booking_Platform_F.Application.DTOs.WriteDTOs.CityWriteDto' ...
2025-07-26 15:43:53.039 +03:00 [DBG] Done attempting to validate the bound parameter 'dto' of type 'Travel_Accommodation_Booking_Platform_F.Application.DTOs.WriteDTOs.CityWriteDto'.
2025-07-26 15:43:53.043 +03:00 [INF] Executing action method Travel_Accommodation_Booking_Platform_F.Controllers.CitiesController.CreateCity (Travel-Accommodation-Booking-Platform-F.API) - Validation state: "Valid"
2025-07-26 15:43:53.046 +03:00 [INF] Create City request received
2025-07-26 15:43:53.051 +03:00 [INF] Create city request received
2025-07-26 15:43:53.052 +03:00 [INF] Correct city information sent
2025-07-26 15:43:53.102 +03:00 [DBG] 'ApplicationDbContext' generated a temporary value for the property 'City.CityId'. Consider using 'DbContextOptionsBuilder.EnableSensitiveDataLogging' to see key values.
2025-07-26 15:43:53.198 +03:00 [DBG] Context 'ApplicationDbContext' started tracking 'City' entity. Consider using 'DbContextOptionsBuilder.EnableSensitiveDataLogging' to see key values.
2025-07-26 15:43:53.206 +03:00 [DBG] SaveChanges starting for 'ApplicationDbContext'.
2025-07-26 15:43:53.212 +03:00 [DBG] DetectChanges starting for 'ApplicationDbContext'.
2025-07-26 15:43:53.244 +03:00 [DBG] DetectChanges completed for 'ApplicationDbContext'.
2025-07-26 15:43:53.351 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:53.353 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:53.358 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:43:53.360 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:43:53.364 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (5ms).
2025-07-26 15:43:53.365 +03:00 [DBG] Executing DbCommand [Parameters=[@p0='?' (Size = 4000), @p1='?' (DbType = DateTime2), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Int32), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Cities] ([Country], [CreatedAt], [LastUpdated], [Name], [NumberOfHotels], [PostOffice], [UpdatedAt])
OUTPUT INSERTED.[CityId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
2025-07-26 15:43:53.372 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@p0='?' (Size = 4000), @p1='?' (DbType = DateTime2), @p2='?' (DbType = DateTime2), @p3='?' (Size = 4000), @p4='?' (DbType = Int32), @p5='?' (Size = 4000), @p6='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Cities] ([Country], [CreatedAt], [LastUpdated], [Name], [NumberOfHotels], [PostOffice], [UpdatedAt])
OUTPUT INSERTED.[CityId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6);
2025-07-26 15:43:53.387 +03:00 [DBG] The foreign key property 'City.CityId' was detected as changed. Consider using 'DbContextOptionsBuilder.EnableSensitiveDataLogging' to see property values.
2025-07-26 15:43:53.396 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:53.397 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 24ms reading results.
2025-07-26 15:43:53.400 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:53.403 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (2ms).
2025-07-26 15:43:53.421 +03:00 [DBG] An entity of type 'City' tracked by 'ApplicationDbContext' changed state from '"Added"' to '"Unchanged"'. Consider using 'DbContextOptionsBuilder.EnableSensitiveDataLogging' to see key values.
2025-07-26 15:43:53.427 +03:00 [DBG] SaveChanges completed for 'ApplicationDbContext' with 1 entities written to the database.
2025-07-26 15:43:53.429 +03:00 [INF] Delete cached data
2025-07-26 15:43:53.432 +03:00 [INF] Registered City success for City: 1003
2025-07-26 15:43:53.434 +03:00 [INF] Executed action method Travel_Accommodation_Booking_Platform_F.Controllers.CitiesController.CreateCity (Travel-Accommodation-Booking-Platform-F.API), returned result Microsoft.AspNetCore.Mvc.CreatedAtActionResult in 389.8669ms.
2025-07-26 15:43:53.436 +03:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-26 15:43:53.440 +03:00 [DBG] No information found on request to perform content negotiation.
2025-07-26 15:43:53.441 +03:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-26 15:43:53.442 +03:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-26 15:43:53.443 +03:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-26 15:43:53.444 +03:00 [INF] Executing CreatedAtActionResult, writing value of type 'Travel_Accommodation_Booking_Platform_F.Application.DTOs.ReadDTOs.CityReadDto'.
2025-07-26 15:43:53.495 +03:00 [DBG] Found the endpoints ["Travel_Accommodation_Booking_Platform_F.Controllers.CitiesController.GetCityById (Travel-Accommodation-Booking-Platform-F.API)"] for address (Id=[1003],version=[1],action=[GetCityById],controller=[Cities])
2025-07-26 15:43:53.502 +03:00 [DBG] Successfully processed template api/v{version:apiVersion}/Cities/{id:int} for Travel_Accommodation_Booking_Platform_F.Controllers.CitiesController.GetCityById (Travel-Accommodation-Booking-Platform-F.API) resulting in /api/v1/Cities/1003 and 
2025-07-26 15:43:53.505 +03:00 [DBG] Link generation succeeded for endpoints ["Travel_Accommodation_Booking_Platform_F.Controllers.CitiesController.GetCityById (Travel-Accommodation-Booking-Platform-F.API)"] with result /api/v1/Cities/1003
2025-07-26 15:43:53.507 +03:00 [INF] Executed action Travel_Accommodation_Booking_Platform_F.Controllers.CitiesController.CreateCity (Travel-Accommodation-Booking-Platform-F.API) in 523.9311ms
2025-07-26 15:43:53.509 +03:00 [INF] Executed endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.CitiesController.CreateCity (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:43:53.510 +03:00 [DBG] Connection id "0HNEC87JSNB88" completed keep alive response.
2025-07-26 15:43:53.511 +03:00 [DBG] 'ApplicationDbContext' disposed.
2025-07-26 15:43:53.512 +03:00 [DBG] Disposing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:43:53.513 +03:00 [DBG] Disposed connection to database '' on server '' (1ms).
2025-07-26 15:43:53.516 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/v1/cities - 201 null application/json; charset=utf-8 591.7251ms
2025-07-26 15:44:00.763 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - null null
2025-07-26 15:44:00.766 +03:00 [DBG] 1 candidate(s) found for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:44:00.767 +03:00 [DBG] Endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)' with route pattern 'api/v{version:apiVersion}/Admins/top-visited-cities' is valid for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:44:00.770 +03:00 [DBG] Request matched endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:44:00.772 +03:00 [DBG] Static files was skipped as the request already matched an endpoint.
2025-07-26 15:44:00.773 +03:00 [DBG] The request is insecure. Skipping HSTS header.
2025-07-26 15:44:00.777 +03:00 [DBG] Successfully validated the token.
2025-07-26 15:44:00.778 +03:00 [INF] Checking if token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is blacklisted.
2025-07-26 15:44:00.780 +03:00 [DBG] Entity Framework Core 9.0.0 initialized 'ApplicationDbContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:9.0.0' with options: EngineType=SqlServer 
2025-07-26 15:44:00.782 +03:00 [DBG] Creating DbConnection.
2025-07-26 15:44:00.783 +03:00 [DBG] Created DbConnection. (1ms).
2025-07-26 15:44:00.784 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:00.786 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:00.788 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:44:00.789 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:44:00.791 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:44:00.793 +03:00 [DBG] Executing DbCommand [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:44:00.796 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:44:00.799 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:00.801 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 2ms reading results.
2025-07-26 15:44:00.804 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:00.805 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:44:00.808 +03:00 [INF] Token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is not blacklisted.
2025-07-26 15:44:00.809 +03:00 [DBG] AuthenticationScheme: Bearer was successfully authenticated.
2025-07-26 15:44:00.810 +03:00 [DBG] Authorization was successful.
2025-07-26 15:44:00.811 +03:00 [INF] Executing endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:44:00.812 +03:00 [INF] Route matched with {action = "GetTopVisitedCities", controller = "Admins"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTopVisitedCities() on controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API).
2025-07-26 15:44:00.814 +03:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-26 15:44:00.815 +03:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-26 15:44:00.818 +03:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:44:00.821 +03:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-26 15:44:00.823 +03:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:44:00.825 +03:00 [DBG] Executing controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:44:00.827 +03:00 [DBG] Executed controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:44:00.828 +03:00 [INF] Executing action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) - Validation state: "Valid"
2025-07-26 15:44:00.830 +03:00 [INF] Get Top Visited Cities request received
2025-07-26 15:44:00.830 +03:00 [INF] Fetching cities from repository
2025-07-26 15:44:00.832 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:00.835 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:00.837 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:44:00.838 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:44:00.840 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:44:00.841 +03:00 [DBG] Executing DbCommand [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:44:00.847 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:44:00.849 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:00.851 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:44:00.853 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:00.855 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:44:00.857 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:00.858 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:00.860 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:44:00.861 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:44:00.863 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:44:00.865 +03:00 [DBG] Executing DbCommand [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:44:00.871 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:44:00.874 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:00.876 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:44:00.877 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:00.878 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:44:00.881 +03:00 [INF] Fetched cities from repository successfully
2025-07-26 15:44:00.882 +03:00 [INF] Check if list of top visited cities not updated
2025-07-26 15:44:00.883 +03:00 [INF] Send ETag to client if list of top visited cities updated recently
2025-07-26 15:44:00.884 +03:00 [INF] Get Top Visited Cities success
2025-07-26 15:44:00.885 +03:00 [INF] Executed action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 55.1455ms.
2025-07-26 15:44:00.886 +03:00 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter","Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2025-07-26 15:44:00.889 +03:00 [DBG] No information found on request to perform content negotiation.
2025-07-26 15:44:00.889 +03:00 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2025-07-26 15:44:00.890 +03:00 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
2025-07-26 15:44:00.891 +03:00 [DBG] Selected output formatter 'Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter' and content type 'application/json' to write the response.
2025-07-26 15:44:00.892 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Travel_Accommodation_Booking_Platform_F.Application.DTOs.ReadDTOs.CityReadDto, Travel-Accommodation-Booking-Platform-F.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-07-26 15:44:00.894 +03:00 [INF] Executed action Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) in 68.6435ms
2025-07-26 15:44:00.896 +03:00 [INF] Executed endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:44:00.897 +03:00 [DBG] Connection id "0HNEC87JSNB88" completed keep alive response.
2025-07-26 15:44:00.899 +03:00 [DBG] 'ApplicationDbContext' disposed.
2025-07-26 15:44:00.900 +03:00 [DBG] Disposing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:00.901 +03:00 [DBG] Disposed connection to database '' on server '' (1ms).
2025-07-26 15:44:00.903 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - 200 null application/json; charset=utf-8 138.9813ms
2025-07-26 15:44:23.863 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - null null
2025-07-26 15:44:23.866 +03:00 [DBG] 1 candidate(s) found for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:44:23.868 +03:00 [DBG] Endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)' with route pattern 'api/v{version:apiVersion}/Admins/top-visited-cities' is valid for the request path '/api/v1/admins/top-visited-cities'
2025-07-26 15:44:23.870 +03:00 [DBG] Request matched endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:44:23.871 +03:00 [DBG] Static files was skipped as the request already matched an endpoint.
2025-07-26 15:44:23.872 +03:00 [DBG] The request is insecure. Skipping HSTS header.
2025-07-26 15:44:23.874 +03:00 [DBG] Successfully validated the token.
2025-07-26 15:44:23.875 +03:00 [INF] Checking if token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is blacklisted.
2025-07-26 15:44:23.877 +03:00 [DBG] Entity Framework Core 9.0.0 initialized 'ApplicationDbContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:9.0.0' with options: EngineType=SqlServer 
2025-07-26 15:44:23.879 +03:00 [DBG] Creating DbConnection.
2025-07-26 15:44:23.879 +03:00 [DBG] Created DbConnection. (0ms).
2025-07-26 15:44:23.881 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:23.892 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:23.894 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:44:23.896 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:44:23.898 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (3ms).
2025-07-26 15:44:23.900 +03:00 [DBG] Executing DbCommand [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:44:23.904 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__jti_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [BlacklistedTokens] AS [b]
        WHERE [b].[Jti] = @__jti_0 AND [b].[Expiration] > GETUTCDATE()) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-07-26 15:44:23.906 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:23.908 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:44:23.910 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:23.912 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:44:23.914 +03:00 [INF] Token with JTI: 0fd5885f-1044-4b48-a995-883b209112f1 is not blacklisted.
2025-07-26 15:44:23.916 +03:00 [DBG] AuthenticationScheme: Bearer was successfully authenticated.
2025-07-26 15:44:23.918 +03:00 [DBG] Authorization was successful.
2025-07-26 15:44:23.919 +03:00 [INF] Executing endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:44:23.920 +03:00 [INF] Route matched with {action = "GetTopVisitedCities", controller = "Admins"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTopVisitedCities() on controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API).
2025-07-26 15:44:23.922 +03:00 [DBG] Execution plan of authorization filters (in the following order): ["None"]
2025-07-26 15:44:23.924 +03:00 [DBG] Execution plan of resource filters (in the following order): ["None"]
2025-07-26 15:44:23.925 +03:00 [DBG] Execution plan of action filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)","Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:44:23.927 +03:00 [DBG] Execution plan of exception filters (in the following order): ["None"]
2025-07-26 15:44:23.929 +03:00 [DBG] Execution plan of result filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)","Microsoft.AspNetCore.Mvc.ReportApiVersionsAttribute (Order: 0)"]
2025-07-26 15:44:23.931 +03:00 [DBG] Executing controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:44:23.933 +03:00 [DBG] Executed controller factory for controller Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController (Travel-Accommodation-Booking-Platform-F.API)
2025-07-26 15:44:23.935 +03:00 [INF] Executing action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) - Validation state: "Valid"
2025-07-26 15:44:23.936 +03:00 [INF] Get Top Visited Cities request received
2025-07-26 15:44:23.937 +03:00 [INF] Fetching cities from repository
2025-07-26 15:44:23.938 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:23.940 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:23.941 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:44:23.942 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-26 15:44:23.944 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:44:23.945 +03:00 [DBG] Executing DbCommand [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:44:23.948 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(@__p_0) [h].[CityId]
FROM [Bookings] AS [b]
INNER JOIN [Rooms] AS [r] ON [b].[RoomId] = [r].[RoomId]
INNER JOIN [Hotels] AS [h] ON [r].[HotelId] = [h].[HotelId]
GROUP BY [h].[CityId]
ORDER BY COUNT(*) DESC
2025-07-26 15:44:23.951 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:23.952 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:44:23.953 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:23.955 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:44:23.957 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:23.959 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:23.960 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-26 15:44:23.961 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (0ms).
2025-07-26 15:44:23.963 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (2ms).
2025-07-26 15:44:23.965 +03:00 [DBG] Executing DbCommand [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:44:23.967 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__topCityIds_0='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[CityId], [c].[Country], [c].[CreatedAt], [c].[LastUpdated], [c].[Name], [c].[NumberOfHotels], [c].[PostOffice], [c].[UpdatedAt]
FROM [Cities] AS [c]
WHERE [c].[CityId] IN (
    SELECT [t].[value]
    FROM OPENJSON(@__topCityIds_0) WITH ([value] int '$') AS [t]
)
2025-07-26 15:44:23.970 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:23.971 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 1ms reading results.
2025-07-26 15:44:23.973 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:23.975 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (1ms).
2025-07-26 15:44:23.976 +03:00 [INF] Fetched cities from repository successfully
2025-07-26 15:44:23.978 +03:00 [INF] Check if list of top visited cities not updated
2025-07-26 15:44:23.979 +03:00 [INF] Retrieved data from browser cache
2025-07-26 15:44:23.981 +03:00 [INF] Executed action method Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API), returned result Microsoft.AspNetCore.Mvc.StatusCodeResult in 44.9036ms.
2025-07-26 15:44:23.983 +03:00 [INF] Executing StatusCodeResult, setting HTTP status code 304
2025-07-26 15:44:23.984 +03:00 [INF] Executed action Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API) in 52.5228ms
2025-07-26 15:44:23.985 +03:00 [INF] Executed endpoint 'Travel_Accommodation_Booking_Platform_F.Controllers.AdminsController.GetTopVisitedCities (Travel-Accommodation-Booking-Platform-F.API)'
2025-07-26 15:44:23.987 +03:00 [DBG] Connection id "0HNEC87JSNB88" completed keep alive response.
2025-07-26 15:44:23.988 +03:00 [DBG] 'ApplicationDbContext' disposed.
2025-07-26 15:44:23.989 +03:00 [DBG] Disposing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-26 15:44:23.990 +03:00 [DBG] Disposed connection to database '' on server '' (1ms).
2025-07-26 15:44:23.992 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/v1/admins/top-visited-cities - 304 null null 128.1177ms
2025-07-26 15:46:35.238 +03:00 [DBG] Connection id "0HNEC87JSNB88" disconnecting.
2025-07-26 15:46:35.241 +03:00 [DBG] Connection id "0HNEC87JSNB88" stopped.
2025-07-26 15:46:35.242 +03:00 [DBG] Connection id "0HNEC87JSNB88" sending FIN because: "The Socket transport's send loop completed gracefully."
2025-07-26 15:50:35.207 +03:00 [INF] Application is shutting down...
2025-07-26 15:50:35.209 +03:00 [DBG] Hosting stopping
2025-07-26 15:50:35.218 +03:00 [DBG] Hosting stopped
