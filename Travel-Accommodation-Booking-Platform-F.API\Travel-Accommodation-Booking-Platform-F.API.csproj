<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <RootNamespace>Travel_Accommodation_Booking_Platform_F</RootNamespace>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <PropertyGroup>
        <PreserveCompilationContext>true</PreserveCompilationContext>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0"/>
        <PackageReference Include="AspNetCoreRateLimit" Version="5.0.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0"/>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4"/>
        <PackageReference Include="NWebsec.AspNetCore.Middleware" Version="3.0.0"/>
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0"/>
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0"/>
        <PackageReference Include="Serilog.Sinks.File" Version="7.0.0"/>
        <PackageReference Include="Swashbuckle.AspNetCore" Version="7.0.0"/>
        <PackageReference Include="MediatR" Version="12.2.0"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Travel-Accommodation-Booking-Platform-F.Application\Travel-Accommodation-Booking-Platform-F.Application.csproj"/>
        <ProjectReference Include="..\Travel-Accommodation-Booking-Platform-F.Infrastructure\Travel-Accommodation-Booking-Platform-F.Infrastructure.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Content Include="..\.dockerignore">
            <Link>.dockerignore</Link>
        </Content>
    </ItemGroup>

</Project>
