﻿using AutoMapper;
using Travel_Accommodation_Booking_Platform_F.Application.DTOs.ReadDTOs;
using Travel_Accommodation_Booking_Platform_F.Application.DTOs.WriteDTOs;
using Travel_Accommodation_Booking_Platform_F.Domain.Entities;

namespace Travel_Accommodation_Booking_Platform_F.Application.Mapping;

public class RoomProfile : Profile
{
    public RoomProfile()
    {
        CreateMap<RoomWriteDto, Room>();
        CreateMap<RoomWriteDto, Room>();
        CreateMap<Room, RoomReadDto>();
        CreateMap<Room, RoomWriteDto>();
    }
}