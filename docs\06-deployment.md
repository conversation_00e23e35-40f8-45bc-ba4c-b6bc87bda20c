# Deployment Guide

## 🚀 Deployment Overview

The Travel Accommodation Booking Platform supports multiple deployment strategies including Docker containers, cloud platforms, and traditional server deployments. This guide covers the recommended deployment approaches for different environments.

## 🐳 Docker Deployment

### Dockerfile Configuration

The application includes a multi-stage Dockerfile for optimized production builds:

```dockerfile
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy project files
COPY Travel-Accommodation-Booking-Platform-F.Application ./Travel-Accommodation-Booking-Platform-F.Application
COPY Travel-Accommodation-Booking-Platform-F.Infrastructure ./Travel-Accommodation-Booking-Platform-F.Infrastructure
COPY Travel-Accommodation-Booking-Platform-F.Domain ./Travel-Accommodation-Booking-Platform-F.Domain
COPY Travel-Accommodation-Booking-Platform-F.API ./Travel-Accommodation-Booking-Platform-F.API

WORKDIR /src/Travel-Accommodation-Booking-Platform-F.API

# Restore and publish
RUN dotnet restore Travel-Accommodation-Booking-Platform-F.API.csproj
RUN dotnet publish Travel-Accommodation-Booking-Platform-F.API.csproj -c Release -o /app/publish

# Runtime image
FROM mcr.microsoft.com/dotnet/aspnet:9.0
WORKDIR /app
COPY --from=build /app/publish .
ENTRYPOINT ["dotnet", "Travel-Accommodation-Booking-Platform-F.API.dll"]
```

### Docker Compose Setup

Create `docker-compose.yml` for local development:

```yaml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "5000:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=TravelBookingDB;User Id=sa;Password=YourPassword123!;TrustServerCertificate=true
    depends_on:
      - sqlserver
    networks:
      - travel-network

  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourPassword123!
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - travel-network

volumes:
  sqlserver_data:

networks:
  travel-network:
    driver: bridge
```

### Docker Commands

```bash
# Build the image
docker build -t travel-booking-api .

# Run the container
docker run -p 5000:8080 travel-booking-api

# Run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f api

# Stop services
docker-compose down
```

## ☁️ Cloud Deployment

### Docker Swarm on AWS EC2

The CI/CD pipeline automatically deploys to Docker Swarm running on AWS EC2. The deployment uses Docker services for high availability and scalability.

#### Prerequisites
- AWS EC2 instance (t3.medium or larger recommended)
- Ubuntu 20.04 LTS or Amazon Linux 2
- Security group allowing HTTP (80), HTTPS (443), SSH (22), and Docker Swarm ports (2377, 7946, 4789)
- Docker Swarm initialized on the EC2 instance

#### EC2 Setup Steps

1. **Connect to EC2 Instance**:
```bash
ssh -i your-key.pem ubuntu@your-ec2-ip
```

2. **Install Docker**:
```bash
sudo apt update
sudo apt install docker.io -y
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker ubuntu
```

3. **Initialize Docker Swarm**:
```bash
docker swarm init
```

4. **Verify Swarm Status**:
```bash
docker node ls
```

#### Automated Deployment Process

The CI/CD pipeline performs the following deployment steps:

1. **Build and Test**: Runs unit and integration tests
2. **Security Scanning**: Uses Trivy for vulnerability and secrets scanning
3. **Docker Build**: Creates and pushes Docker image to Docker Hub
4. **SSH Deployment**: Connects to EC2 and updates/creates Docker service
5. **Slack Notifications**: Sends deployment status to Slack

#### Docker Service Configuration

The pipeline creates a Docker service with the following configuration:

```bash
docker service create --name myproject_api \
  --replicas 1 \
  -p 5000:80 \
  --env SECRET_KEY=$SECRET_KEY \
  --env TRAVEL_ACCOMMODATION_CONNECTION_STRING="$CONNECTION_STRING" \
  --env APP_PASSWORD=$APP_PASSWORD \
  abdullahgsholi/myproject-api:latest
```

#### Service Management Commands

```bash
# Check service status
docker service ls

# View service logs
docker service logs myproject_api

# Scale the service
docker service scale myproject_api=3

# Update service with new image
docker service update --image abdullahgsholi/myproject-api:latest myproject_api

# Remove service
docker service rm myproject_api
```

### Deployment Notifications

The CI/CD pipeline includes Slack notifications for deployment status updates. This provides real-time feedback to the development team about deployment success or failure.

#### Slack Integration

The pipeline sends notifications to Slack in the following scenarios:

1. **Successful Deployment**:
```
🚀 The update was successfully deployed to Docker Swarm!
```

2. **Failed Deployment**:
```
❌ *Failure:* Deployment to Docker Swarm failed. Please check the pipeline logs. 🚨
```

3. **Cancelled Deployment**:
```
⚠️ *Cancelled:* The deployment process was cancelled.
```

#### Setting Up Slack Notifications

1. **Create Slack App**:
   - Go to https://api.slack.com/apps
   - Click "Create New App" and select "From scratch"
   - Name your app and select your workspace

2. **Enable Incoming Webhooks**:
   - Navigate to "Incoming Webhooks" in the sidebar
   - Toggle "Activate Incoming Webhooks" to On
   - Click "Add New Webhook to Workspace"
   - Select the channel to post notifications to

3. **Copy Webhook URL**:
   - Copy the Webhook URL provided
   - Add it as a GitHub repository secret named `SLACK_WEBHOOK_URL`

4. **Customize Notifications**:
   - Edit the custom payload in the GitHub Actions workflow to change notification format
   - Add additional fields or formatting as needed

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow

The project includes a comprehensive GitHub Actions workflow (`.github/workflows/deploy.yml`) that deploys to Docker Swarm on EC2:

```yaml
name: Deploy to Docker Swarm on EC2

on:
  push:
    branches:
      - feat/setup-ci-cd-pipeline

permissions:
  contents: read
  security-events: write

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    env:
      DOTNET_VERSION: '9.0.203'
      TRAVEL_ACCOMMODATION_CONNECTION_STRING: ${{ secrets.SQLSERVER_CONNECTIONSTRING }}
      SECRET_KEY: ${{ secrets.SECRET_KEY }}
      APP_PASSWORD: ${{ secrets.APP_PASSWORD }}

    steps:
    - name: Checkout source code
      uses: actions/checkout@v3

    - name: Setup .NET SDK
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --no-restore --configuration Release

    - name: Run Unit Tests
      run: dotnet test --no-build --configuration Release --filter "Category~UnitTests"
      env:
        ConnectionStrings__DefaultConnection: ${{ env.TRAVEL_ACCOMMODATION_CONNECTION_STRING }}

    - name: Run Integration Tests
      run: dotnet test --no-build --configuration Release --filter "Category~IntegrationTests"
      env:
        ConnectionStrings__DefaultConnection: ${{ env.TRAVEL_ACCOMMODATION_CONNECTION_STRING }}

    - name: Publish application for Linux
      run: dotnet publish ./Travel-Accommodation-Booking-Platform-F.API/Travel-Accommodation-Booking-Platform-F.API.csproj -c Release -o ./publish --runtime linux-x64 --self-contained false

    - name: Upload published app
      uses: actions/upload-artifact@v4
      with:
        name: published-app
        path: ./publish

  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to DockerHub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Travel-Accommodation-Booking-Platform-F.API/Dockerfile
        push: false
        tags: abdullahgsholi/myproject-api:latest

    - name: Run Trivy secrets scan
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: fs
        scan-ref: .
        scanners: secret
        format: table
        exit-code: 1

    - name: Run Trivy vulnerability scan on Docker image
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: abdullahgsholi/myproject-api:latest
        format: table
        exit-code: 1
        ignore-unfixed: true
        severity: HIGH,CRITICAL

    - name: Run Trivy SARIF report
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: abdullahgsholi/myproject-api:latest
        format: sarif
        output: trivy-results.sarif

    - name: Upload Trivy SARIF to GitHub Security
      if: github.ref == 'refs/heads/main'
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: trivy-results.sarif

    - name: Push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Travel-Accommodation-Booking-Platform-F.API/Dockerfile
        push: true
        tags: abdullahgsholi/myproject-api:latest

    - name: SSH and deploy to EC2
      uses: appleboy/ssh-action@v0.1.7
      with:
        host: ${{ secrets.EC2_HOST }}
        username: ubuntu
        key: ${{ secrets.EC2_SSH_KEY }}
        script: |
          docker service update \
            --env SECRET_KEY=${{ secrets.SECRET_KEY }} \
            --env TRAVEL_ACCOMMODATION_CONNECTION_STRING="${{ secrets.SQLSERVER_CONNECTIONSTRING }}" \
            --env APP_PASSWORD=${{ secrets.APP_PASSWORD }} \
            --image abdullahgsholi/myproject-api:latest myproject_api || \
          docker service create --name myproject_api --replicas 1 -p 5000:80 \
            --env SECRET_KEY=${{ secrets.SECRET_KEY }} \
            --env TRAVEL_ACCOMMODATION_CONNECTION_STRING="${{ secrets.SQLSERVER_CONNECTIONSTRING }}" \
            --env APP_PASSWORD=${{ secrets.APP_PASSWORD }} \
            abdullahgsholi/myproject_api:latest

    - name: Send Slack notification
      if: success()
      uses: 8398a7/action-slack@v3
      with:
        status: success
        fields: repo,message,commit,author
        custom_payload: |
          {
            "text": "🚀 The update was successfully deployed to Docker Swarm!"
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Send Slack notification on Failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        fields: repo,message,commit,author
        custom_payload: |
          {
            "text": "❌ *Failure:* Deployment to Docker Swarm failed. Please check the pipeline logs. 🚨"
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Send Slack notification on Cancelled
      if: cancelled()
      uses: 8398a7/action-slack@v3
      with:
        status: cancelled
        fields: repo,message,commit,author
        custom_payload: |
          {
            "text": "⚠️ *Cancelled:* The deployment process was cancelled."
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

### Required Secrets

Configure these secrets in your GitHub repository:

- `DOCKERHUB_USERNAME`: Docker Hub username
- `DOCKERHUB_TOKEN`: Docker Hub access token
- `EC2_HOST`: EC2 instance public IP or hostname
- `EC2_SSH_KEY`: Private SSH key for EC2 access
- `SQLSERVER_CONNECTIONSTRING`: Production SQL Server connection string
- `SECRET_KEY`: JWT secret key for token signing
- `APP_PASSWORD`: Email service application password
- `SLACK_WEBHOOK_URL`: Slack webhook URL for notifications

## 🧪 Pipeline Testing Strategy

The CI/CD pipeline implements a comprehensive testing strategy before deployment:

### Test Categories

#### Unit Tests
```bash
dotnet test --no-build --configuration Release --filter "Category~UnitTests"
```
- Tests individual components in isolation
- Fast execution and no external dependencies
- Validates business logic and service layer functionality

#### Integration Tests
```bash
dotnet test --no-build --configuration Release --filter "Category~IntegrationTests"
```
- Tests component interactions with real database
- Uses TestContainers for database isolation
- Validates API endpoints and data access layer

### Security Scanning

The pipeline includes multiple security scanning steps:

#### 1. Secrets Scanning
```yaml
- name: Run Trivy secrets scan
  uses: aquasecurity/trivy-action@master
  with:
    scan-type: fs
    scan-ref: .
    scanners: secret
    format: table
    exit-code: 1
```

#### 2. Vulnerability Scanning
```yaml
- name: Run Trivy vulnerability scan on Docker image
  uses: aquasecurity/trivy-action@master
  with:
    image-ref: abdullahgsholi/myproject-api:latest
    format: table
    exit-code: 1
    ignore-unfixed: true
    severity: HIGH,CRITICAL
```

#### 3. SARIF Security Reports
```yaml
- name: Run Trivy SARIF report
  uses: aquasecurity/trivy-action@master
  with:
    image-ref: abdullahgsholi/myproject-api:latest
    format: sarif
    output: trivy-results.sarif
```

The SARIF reports are uploaded to GitHub Security tab for centralized vulnerability management.

### Build Artifacts

The pipeline creates and manages build artifacts:

```yaml
- name: Publish application for Linux
  run: dotnet publish ./Travel-Accommodation-Booking-Platform-F.API/Travel-Accommodation-Booking-Platform-F.API.csproj -c Release -o ./publish --runtime linux-x64 --self-contained false

- name: Upload published app
  uses: actions/upload-artifact@v4
  with:
    name: published-app
    path: ./publish
```

## 🔧 Production Configuration

### Environment Variables

The Docker Swarm service uses the following environment variables:

```bash
# Application Settings
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=http://+:80

# Database Connection
TRAVEL_ACCOMMODATION_CONNECTION_STRING=Server=your-sql-server;Database=TravelBookingDB;User Id=sa;Password=your-password;TrustServerCertificate=true

# JWT Secret Key
SECRET_KEY=your-super-secret-jwt-key-minimum-32-characters

# Email Service Password
APP_PASSWORD=your-email-app-password

# Logging
LOG_PATH=/app/logs
```

### Docker Service Environment Configuration

The CI/CD pipeline automatically configures these environment variables when creating or updating the Docker service:

```bash
docker service create --name myproject_api \
  --replicas 1 \
  -p 5000:80 \
  --env SECRET_KEY=$SECRET_KEY \
  --env TRAVEL_ACCOMMODATION_CONNECTION_STRING="$CONNECTION_STRING" \
  --env APP_PASSWORD=$APP_PASSWORD \
  abdullahgsholi/myproject-api:latest
```

### Environment Variable Security

- All sensitive environment variables are stored as GitHub Secrets
- Variables are injected at deployment time, not stored in the Docker image
- Connection strings and keys are encrypted in transit and at rest

### SSL/TLS Configuration

#### Using Let's Encrypt with Nginx

1. **Install Nginx**:
```bash
sudo apt install nginx certbot python3-certbot-nginx
```

2. **Configure Nginx**:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

3. **Obtain SSL Certificate**:
```bash
sudo certbot --nginx -d your-domain.com
```

### Health Checks

Add health check endpoints for monitoring:

```csharp
// In Program.cs
app.MapHealthChecks("/health");
app.MapHealthChecks("/health/ready");
app.MapHealthChecks("/health/live");
```

### Monitoring Setup

#### Application Insights (Azure)
```csharp
builder.Services.AddApplicationInsightsTelemetry();
```

#### Prometheus Metrics
```csharp
builder.Services.AddPrometheusMetrics();
app.UsePrometheusMetrics();
```

## 📊 Performance Optimization

### Production Optimizations

1. **Enable Response Compression**:
```csharp
builder.Services.AddResponseCompression();
app.UseResponseCompression();
```

2. **Configure Caching**:
```csharp
builder.Services.AddMemoryCache();
builder.Services.AddResponseCaching();
app.UseResponseCaching();
```

3. **Database Connection Pooling**:
```csharp
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString, sqlOptions =>
    {
        sqlOptions.EnableRetryOnFailure();
        sqlOptions.CommandTimeout(30);
    }));
```

### Load Balancing

For high-traffic scenarios, consider:
- **Application Load Balancer** (AWS ALB)
- **Azure Load Balancer**
- **Nginx Load Balancer**

Example Nginx load balancer configuration:
```nginx
upstream api_servers {
    server 10.0.1.10:5000;
    server 10.0.1.11:5000;
    server 10.0.1.12:5000;
}

server {
    listen 80;
    location / {
        proxy_pass http://api_servers;
    }
}
```

---

**Continue to**: [Security Documentation](07-security.md) for security best practices.
