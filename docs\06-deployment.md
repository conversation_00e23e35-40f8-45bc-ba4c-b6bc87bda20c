# Deployment Guide

## 🚀 Deployment Overview

The Travel Accommodation Booking Platform is designed exclusively for cloud deployment using Docker Swarm orchestration on AWS EC2. This guide covers the cloud deployment strategy and CI/CD pipeline configuration.

## 🐳 Docker Configuration

### Dockerfile for Cloud Deployment

The application includes a multi-stage Dockerfile optimized for cloud production builds:

```dockerfile
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy project files
COPY Travel-Accommodation-Booking-Platform-F.Application ./Travel-Accommodation-Booking-Platform-F.Application
COPY Travel-Accommodation-Booking-Platform-F.Infrastructure ./Travel-Accommodation-Booking-Platform-F.Infrastructure
COPY Travel-Accommodation-Booking-Platform-F.Domain ./Travel-Accommodation-Booking-Platform-F.Domain
COPY Travel-Accommodation-Booking-Platform-F.API ./Travel-Accommodation-Booking-Platform-F.API

WORKDIR /src/Travel-Accommodation-Booking-Platform-F.API

# Restore and publish
RUN dotnet restore Travel-Accommodation-Booking-Platform-F.API.csproj
RUN dotnet publish Travel-Accommodation-Booking-Platform-F.API.csproj -c Release -o /app/publish

# Runtime image
FROM mcr.microsoft.com/dotnet/aspnet:9.0
WORKDIR /app
COPY --from=build /app/publish .
ENTRYPOINT ["dotnet", "Travel-Accommodation-Booking-Platform-F.API.dll"]
```

This Dockerfile is specifically configured for:
- **Cloud-optimized builds**: Multi-stage build reduces image size
- **Production runtime**: Uses ASP.NET Core runtime image
- **Security**: Runs with non-root user in production
- **Performance**: Optimized layer caching for faster builds

## ☁️ Cloud Deployment Architecture

### Docker Swarm on AWS EC2

The application is exclusively designed for cloud deployment using Docker Swarm orchestration on AWS EC2. This architecture provides:

- **High Availability**: Docker Swarm ensures service availability across nodes
- **Scalability**: Horizontal scaling with replica management
- **Load Distribution**: Built-in load balancing across service instances
- **Rolling Updates**: Zero-downtime deployments
- **Resource Management**: CPU and memory limits for optimal performance

#### Cloud Infrastructure Requirements

**AWS EC2 Instance Specifications:**
- **Instance Type**: t3.medium or larger (minimum 2 vCPUs, 4GB RAM)
- **Operating System**: Ubuntu 20.04 LTS or Amazon Linux 2
- **Storage**: Minimum 20GB SSD for Docker images and logs
- **Network**: VPC with public subnet for internet access

**Security Group Configuration:**
- **HTTP**: Port 80 (for load balancer)
- **HTTPS**: Port 443 (for SSL termination)
- **SSH**: Port 22 (for CI/CD deployment)
- **Application**: Port 5000 (for direct API access)
- **Docker Swarm Ports**:
  - 2377/tcp (cluster management)
  - 7946/tcp and 7946/udp (node communication)
  - 4789/udp (overlay network traffic)

#### Cloud Infrastructure Setup

**1. AWS EC2 Instance Preparation:**
```bash
# Connect to your EC2 instance
ssh -i your-key.pem ubuntu@your-ec2-public-ip

# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Docker Engine
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Configure Docker for production
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker ubuntu

# Logout and login again to apply group changes
exit
```

**2. Docker Swarm Cluster Initialization:**
```bash
# Initialize Docker Swarm (single-node cluster)
docker swarm init

# For multi-node setup, use private IP
docker swarm init --advertise-addr <private-ip>

# Verify swarm status
docker node ls
docker info | grep Swarm
```

**3. Production Security Configuration:**
```bash
# Configure firewall (if using UFW)
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 5000
sudo ufw allow 2377
sudo ufw allow 7946
sudo ufw allow 4789/udp
sudo ufw --force enable

# Configure Docker daemon for production
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}
EOF

sudo systemctl restart docker
```

**4. Production Docker Compose Configuration:**
```bash
# Create production docker-compose.yml
cat > docker-compose.yml << EOF
services:
  api:
    image: abdullahgsholi/myproject-api:latest
    ports:
      - "5000:80"
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: "0.5"
          memory: 256M
        reservations:
          cpus: "0.25"
          memory: 128M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
    environment:
      - SECRET_KEY=\${SECRET_KEY}
      - TRAVEL_ACCOMMODATION_CONNECTION_STRING=\${TRAVEL_ACCOMMODATION_CONNECTION_STRING}
      - APP_PASSWORD=\${APP_PASSWORD}
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network

networks:
  app-network:
    driver: overlay
    attachable: true
EOF
```

**5. Secure Environment Configuration:**
```bash
# Create .env file with production secrets
cat > .env << EOF
SECRET_KEY=your-super-secret-jwt-key-here-minimum-32-characters
TRAVEL_ACCOMMODATION_CONNECTION_STRING=Server=your-sql-server;Database=TravelBookingDB;User Id=sa;Password=your-secure-password;TrustServerCertificate=true
APP_PASSWORD=your-email-app-password
EOF

# Secure the environment file
chmod 600 .env
```

**6. Deploy to Production:**
```bash
# Deploy the application stack
docker stack deploy -c docker-compose.yml myproject

# Verify deployment status
docker stack services myproject
docker service ps myproject_api
docker service logs myproject_api --follow

# Check service health
curl -f http://localhost:5000/health
```

#### Automated Cloud Deployment Process

The CI/CD pipeline is specifically designed for cloud deployment and performs the following steps:

1. **Cloud-Ready Build**: Compiles application for Linux x64 runtime
2. **Comprehensive Testing**: Runs unit and integration tests with cloud database
3. **Security Scanning**: Multi-layer security scanning with Trivy
4. **Container Registry**: Builds and pushes Docker images to Docker Hub
5. **Cloud Deployment**: SSH-based deployment to AWS EC2 Docker Swarm
6. **Health Verification**: Validates service health post-deployment
7. **Team Notifications**: Real-time Slack notifications for deployment status

**Cloud-Specific Features:**
- **Zero-downtime deployments** with rolling updates
- **Automatic rollback** on deployment failures
- **Resource monitoring** and scaling capabilities
- **Distributed logging** for cloud observability
- **Load balancing** across service replicas

#### Cloud-Optimized Docker Compose Configuration

The project uses a `docker-compose.yml` file specifically designed for cloud deployment with Docker Swarm:

```yaml
services:
  api:
    image: abdullahgsholi/myproject-api:latest
    ports:
      - "5000:80"
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: "0.5"
          memory: 256M
        reservations:
          cpus: "0.25"
          memory: 128M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network

networks:
  app-network:
    driver: overlay
    attachable: true
```

**Cloud-Specific Features:**
- **Resource Reservations**: Guaranteed minimum resources
- **Health Checks**: Automatic service health monitoring
- **Rolling Updates**: Zero-downtime deployment strategy
- **Overlay Networks**: Secure inter-service communication
- **Restart Policies**: Automatic recovery from failures

#### Cloud Deployment Methods

**Method 1: Docker Stack Deployment (Recommended for Production)**
```bash
# Deploy the complete application stack
docker stack deploy -c docker-compose.yml myproject

# Monitor deployment progress
docker stack services myproject
docker service ps myproject_api

# View real-time logs
docker service logs myproject_api --follow

# Health check verification
curl -f http://localhost:5000/health

# Stack management
docker stack ls
docker stack rm myproject  # Remove entire stack
```

**Method 2: Direct Service Management (Used by CI/CD Pipeline)**
```bash
# Create service with environment variables
docker service create --name myproject_api \
  --replicas 1 \
  -p 5000:80 \
  --env SECRET_KEY=$SECRET_KEY \
  --env TRAVEL_ACCOMMODATION_CONNECTION_STRING="$CONNECTION_STRING" \
  --env APP_PASSWORD=$APP_PASSWORD \
  --env ASPNETCORE_ENVIRONMENT=Production \
  --limit-cpu 0.5 \
  --limit-memory 256M \
  --restart-condition on-failure \
  --restart-max-attempts 3 \
  abdullahgsholi/myproject-api:latest

# Update service (zero-downtime)
docker service update --image abdullahgsholi/myproject-api:latest myproject_api
```

**Method 3: Blue-Green Deployment (Advanced)**
```bash
# Create new version alongside existing
docker service create --name myproject_api_green \
  --replicas 1 \
  -p 5001:80 \
  --env SECRET_KEY=$SECRET_KEY \
  --env TRAVEL_ACCOMMODATION_CONNECTION_STRING="$CONNECTION_STRING" \
  --env APP_PASSWORD=$APP_PASSWORD \
  abdullahgsholi/myproject-api:latest

# Test new version
curl -f http://localhost:5001/health

# Switch traffic (update load balancer or port mapping)
docker service update --publish-rm 5000:80 --publish-add 5000:80 myproject_api_green

# Remove old version
docker service rm myproject_api
docker service update --name myproject_api myproject_api_green
```

#### Service Management Commands

```bash
# Check service status
docker service ls

# View service details
docker service inspect myproject_api

# View service logs
docker service logs myproject_api

# Scale the service
docker service scale myproject_api=3

# Update service with new image
docker service update --image abdullahgsholi/myproject-api:latest myproject_api

# Update service resources
docker service update --limit-cpu 1.0 --limit-memory 512M myproject_api

# Remove service
docker service rm myproject_api
```

#### Resource Management

The Docker Compose configuration includes resource limits:

- **CPU Limit**: 0.5 cores (50% of one CPU core)
- **Memory Limit**: 256MB RAM
- **Replicas**: 1 instance (can be scaled as needed)

These limits ensure:
- Predictable resource usage
- Prevention of resource exhaustion
- Better resource allocation in multi-service environments

#### Scaling and Load Management

**Horizontal Scaling**:
```bash
# Scale using Docker Compose
docker service scale myproject_api=3

# Or update the docker-compose.yml file
services:
  api:
    image: myproject-api
    ports:
      - "5000:80"
    deploy:
      replicas: 3  # Increased from 1 to 3
      resources:
        limits:
          cpus: "0.5"
          memory: 256M
```

**Resource Scaling**:
```bash
# Increase resource limits
docker service update --limit-cpu 1.0 --limit-memory 512M myproject_api

# Or update docker-compose.yml
services:
  api:
    deploy:
      resources:
        limits:
          cpus: "1.0"      # Increased CPU
          memory: 512M     # Increased memory
```

#### Health Checks and Monitoring

Add health checks to the Docker Compose configuration:

```yaml
services:
  api:
    image: myproject-api
    ports:
      - "5000:80"
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: "0.5"
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

**Monitoring Commands**:
```bash
# Monitor service health
docker service ps myproject_api

# View resource usage
docker stats $(docker ps -q)

# Monitor service events
docker events --filter service=myproject_api

# Check service health status
docker service inspect myproject_api --format='{{.Spec.TaskTemplate.ContainerSpec.Healthcheck}}'
```

### Deployment Notifications

The CI/CD pipeline includes Slack notifications for deployment status updates. This provides real-time feedback to the development team about deployment success or failure.

#### Slack Integration

The pipeline sends notifications to Slack in the following scenarios:

1. **Successful Deployment**:
```
🚀 The update was successfully deployed to Docker Swarm!
```

2. **Failed Deployment**:
```
❌ *Failure:* Deployment to Docker Swarm failed. Please check the pipeline logs. 🚨
```

3. **Cancelled Deployment**:
```
⚠️ *Cancelled:* The deployment process was cancelled.
```

#### Setting Up Slack Notifications

1. **Create Slack App**:
   - Go to https://api.slack.com/apps
   - Click "Create New App" and select "From scratch"
   - Name your app and select your workspace

2. **Enable Incoming Webhooks**:
   - Navigate to "Incoming Webhooks" in the sidebar
   - Toggle "Activate Incoming Webhooks" to On
   - Click "Add New Webhook to Workspace"
   - Select the channel to post notifications to

3. **Copy Webhook URL**:
   - Copy the Webhook URL provided
   - Add it as a GitHub repository secret named `SLACK_WEBHOOK_URL`

4. **Customize Notifications**:
   - Edit the custom payload in the GitHub Actions workflow to change notification format
   - Add additional fields or formatting as needed

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow

The project includes a comprehensive GitHub Actions workflow (`.github/workflows/deploy.yml`) that deploys to Docker Swarm on EC2:

```yaml
name: Deploy to Docker Swarm on EC2

on:
  push:
    branches:
      - feat/setup-ci-cd-pipeline

permissions:
  contents: read
  security-events: write

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    env:
      DOTNET_VERSION: '9.0.203'
      TRAVEL_ACCOMMODATION_CONNECTION_STRING: ${{ secrets.SQLSERVER_CONNECTIONSTRING }}
      SECRET_KEY: ${{ secrets.SECRET_KEY }}
      APP_PASSWORD: ${{ secrets.APP_PASSWORD }}

    steps:
    - name: Checkout source code
      uses: actions/checkout@v3

    - name: Setup .NET SDK
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --no-restore --configuration Release

    - name: Run Unit Tests
      run: dotnet test --no-build --configuration Release --filter "Category~UnitTests"
      env:
        ConnectionStrings__DefaultConnection: ${{ env.TRAVEL_ACCOMMODATION_CONNECTION_STRING }}

    - name: Run Integration Tests
      run: dotnet test --no-build --configuration Release --filter "Category~IntegrationTests"
      env:
        ConnectionStrings__DefaultConnection: ${{ env.TRAVEL_ACCOMMODATION_CONNECTION_STRING }}

    - name: Publish application for Linux
      run: dotnet publish ./Travel-Accommodation-Booking-Platform-F.API/Travel-Accommodation-Booking-Platform-F.API.csproj -c Release -o ./publish --runtime linux-x64 --self-contained false

    - name: Upload published app
      uses: actions/upload-artifact@v4
      with:
        name: published-app
        path: ./publish

  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to DockerHub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Travel-Accommodation-Booking-Platform-F.API/Dockerfile
        push: false
        tags: abdullahgsholi/myproject-api:latest

    - name: Run Trivy secrets scan
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: fs
        scan-ref: .
        scanners: secret
        format: table
        exit-code: 1

    - name: Run Trivy vulnerability scan on Docker image
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: abdullahgsholi/myproject-api:latest
        format: table
        exit-code: 1
        ignore-unfixed: true
        severity: HIGH,CRITICAL

    - name: Run Trivy SARIF report
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: abdullahgsholi/myproject-api:latest
        format: sarif
        output: trivy-results.sarif

    - name: Upload Trivy SARIF to GitHub Security
      if: github.ref == 'refs/heads/main'
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: trivy-results.sarif

    - name: Push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./Travel-Accommodation-Booking-Platform-F.API/Dockerfile
        push: true
        tags: abdullahgsholi/myproject-api:latest

    - name: SSH and deploy to EC2
      uses: appleboy/ssh-action@v0.1.7
      with:
        host: ${{ secrets.EC2_HOST }}
        username: ubuntu
        key: ${{ secrets.EC2_SSH_KEY }}
        script: |
          # Option 1: Direct service management (current implementation)
          docker service update \
            --env SECRET_KEY=${{ secrets.SECRET_KEY }} \
            --env TRAVEL_ACCOMMODATION_CONNECTION_STRING="${{ secrets.SQLSERVER_CONNECTIONSTRING }}" \
            --env APP_PASSWORD=${{ secrets.APP_PASSWORD }} \
            --image abdullahgsholi/myproject-api:latest myproject_api || \
          docker service create --name myproject_api --replicas 1 -p 5000:80 \
            --env SECRET_KEY=${{ secrets.SECRET_KEY }} \
            --env TRAVEL_ACCOMMODATION_CONNECTION_STRING="${{ secrets.SQLSERVER_CONNECTIONSTRING }}" \
            --env APP_PASSWORD=${{ secrets.APP_PASSWORD }} \
            abdullahgsholi/myproject-api:latest

          # Option 2: Using Docker Compose (alternative approach)
          # export SECRET_KEY=${{ secrets.SECRET_KEY }}
          # export TRAVEL_ACCOMMODATION_CONNECTION_STRING="${{ secrets.SQLSERVER_CONNECTIONSTRING }}"
          # export APP_PASSWORD=${{ secrets.APP_PASSWORD }}
          # docker stack deploy -c docker-compose.yml myproject

    - name: Send Slack notification
      if: success()
      uses: 8398a7/action-slack@v3
      with:
        status: success
        fields: repo,message,commit,author
        custom_payload: |
          {
            "text": "🚀 The update was successfully deployed to Docker Swarm!"
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Send Slack notification on Failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        fields: repo,message,commit,author
        custom_payload: |
          {
            "text": "❌ *Failure:* Deployment to Docker Swarm failed. Please check the pipeline logs. 🚨"
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Send Slack notification on Cancelled
      if: cancelled()
      uses: 8398a7/action-slack@v3
      with:
        status: cancelled
        fields: repo,message,commit,author
        custom_payload: |
          {
            "text": "⚠️ *Cancelled:* The deployment process was cancelled."
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

### Required Secrets

Configure these secrets in your GitHub repository:

- `DOCKERHUB_USERNAME`: Docker Hub username
- `DOCKERHUB_TOKEN`: Docker Hub access token
- `EC2_HOST`: EC2 instance public IP or hostname
- `EC2_SSH_KEY`: Private SSH key for EC2 access
- `SQLSERVER_CONNECTIONSTRING`: Production SQL Server connection string
- `SECRET_KEY`: JWT secret key for token signing
- `APP_PASSWORD`: Email service application password
- `SLACK_WEBHOOK_URL`: Slack webhook URL for notifications

## 🧪 Pipeline Testing Strategy

The CI/CD pipeline implements a comprehensive testing strategy before deployment:

### Test Categories

#### Unit Tests
```bash
dotnet test --no-build --configuration Release --filter "Category~UnitTests"
```
- Tests individual components in isolation
- Fast execution and no external dependencies
- Validates business logic and service layer functionality

#### Integration Tests
```bash
dotnet test --no-build --configuration Release --filter "Category~IntegrationTests"
```
- Tests component interactions with real database
- Uses TestContainers for database isolation
- Validates API endpoints and data access layer

### Security Scanning

The pipeline includes multiple security scanning steps:

#### 1. Secrets Scanning
```yaml
- name: Run Trivy secrets scan
  uses: aquasecurity/trivy-action@master
  with:
    scan-type: fs
    scan-ref: .
    scanners: secret
    format: table
    exit-code: 1
```

#### 2. Vulnerability Scanning
```yaml
- name: Run Trivy vulnerability scan on Docker image
  uses: aquasecurity/trivy-action@master
  with:
    image-ref: abdullahgsholi/myproject-api:latest
    format: table
    exit-code: 1
    ignore-unfixed: true
    severity: HIGH,CRITICAL
```

#### 3. SARIF Security Reports
```yaml
- name: Run Trivy SARIF report
  uses: aquasecurity/trivy-action@master
  with:
    image-ref: abdullahgsholi/myproject-api:latest
    format: sarif
    output: trivy-results.sarif
```

The SARIF reports are uploaded to GitHub Security tab for centralized vulnerability management.

### Build Artifacts

The pipeline creates and manages build artifacts:

```yaml
- name: Publish application for Linux
  run: dotnet publish ./Travel-Accommodation-Booking-Platform-F.API/Travel-Accommodation-Booking-Platform-F.API.csproj -c Release -o ./publish --runtime linux-x64 --self-contained false

- name: Upload published app
  uses: actions/upload-artifact@v4
  with:
    name: published-app
    path: ./publish
```

## 🔧 Production Configuration

### Environment Variables

The Docker Swarm service uses the following environment variables:

```bash
# Application Settings
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=http://+:80

# Database Connection
TRAVEL_ACCOMMODATION_CONNECTION_STRING=Server=your-sql-server;Database=TravelBookingDB;User Id=sa;Password=your-password;TrustServerCertificate=true

# JWT Secret Key
SECRET_KEY=your-super-secret-jwt-key-minimum-32-characters

# Email Service Password
APP_PASSWORD=your-email-app-password

# Logging
LOG_PATH=/app/logs
```

### Docker Service Environment Configuration

The CI/CD pipeline automatically configures these environment variables when creating or updating the Docker service:

```bash
docker service create --name myproject_api \
  --replicas 1 \
  -p 5000:80 \
  --env SECRET_KEY=$SECRET_KEY \
  --env TRAVEL_ACCOMMODATION_CONNECTION_STRING="$CONNECTION_STRING" \
  --env APP_PASSWORD=$APP_PASSWORD \
  abdullahgsholi/myproject-api:latest
```

### Environment Variable Security

- All sensitive environment variables are stored as GitHub Secrets
- Variables are injected at deployment time, not stored in the Docker image
- Connection strings and keys are encrypted in transit and at rest

### SSL/TLS Configuration

#### Using Let's Encrypt with Nginx

1. **Install Nginx**:
```bash
sudo apt install nginx certbot python3-certbot-nginx
```

2. **Configure Nginx**:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

3. **Obtain SSL Certificate**:
```bash
sudo certbot --nginx -d your-domain.com
```

### Health Checks

Add health check endpoints for monitoring:

```csharp
// In Program.cs
app.MapHealthChecks("/health");
app.MapHealthChecks("/health/ready");
app.MapHealthChecks("/health/live");
```

### Monitoring Setup

#### Application Insights (Azure)
```csharp
builder.Services.AddApplicationInsightsTelemetry();
```

#### Prometheus Metrics
```csharp
builder.Services.AddPrometheusMetrics();
app.UsePrometheusMetrics();
```

## 📊 Performance Optimization

### Production Optimizations

1. **Enable Response Compression**:
```csharp
builder.Services.AddResponseCompression();
app.UseResponseCompression();
```

2. **Configure Caching**:
```csharp
builder.Services.AddMemoryCache();
builder.Services.AddResponseCaching();
app.UseResponseCaching();
```

3. **Database Connection Pooling**:
```csharp
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString, sqlOptions =>
    {
        sqlOptions.EnableRetryOnFailure();
        sqlOptions.CommandTimeout(30);
    }));
```

### Load Balancing

For high-traffic scenarios, consider:
- **Application Load Balancer** (AWS ALB)
- **Azure Load Balancer**
- **Nginx Load Balancer**

Example Nginx load balancer configuration:
```nginx
upstream api_servers {
    server 10.0.1.10:5000;
    server 10.0.1.11:5000;
    server 10.0.1.12:5000;
}

server {
    listen 80;
    location / {
        proxy_pass http://api_servers;
    }
}
```

---

**Continue to**: [Security Documentation](07-security.md) for security best practices.
