﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoFixture" Version="4.18.1"/>
        <PackageReference Include="AutoFixture.AutoMoq" Version="4.18.1"/>
        <PackageReference Include="AutoFixture.Xunit2" Version="4.18.1"/>
        <PackageReference Include="Moq" Version="4.20.72"/>
        <PackageReference Include="xunit" Version="2.9.3"/>
        <PackageReference Include="xunit.runner.visualstudio" Version="3.1.1">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.7.0"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Travel-Accommodation-Booking-Platform-F.Infrastructure\Travel-Accommodation-Booking-Platform-F.Infrastructure.csproj"/>
    </ItemGroup>

</Project>
