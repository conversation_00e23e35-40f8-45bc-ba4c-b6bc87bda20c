{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Jwt": {"Issuer": "http://localhost:5000", "Audience": "http://localhost:5000", "ExpiresInMinutes": 60}, "ConnectionStrings": {"DefaultConnection": ""}, "IpRateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 10}]}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "Port": 587, "SenderName": "Travel Accommodation System", "SenderEmail": "<EMAIL>", "Username": "<EMAIL>", "Password": ""}}