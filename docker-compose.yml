services:
  api:
    image: myproject-api
    ports:
      - "5000:80"
    environment: 
      - APP_PASSWORD
      - CAR_RENTAL_CONNECTION_STRING
      - SECRET_KEY
      - LOG_PATH=/app/Logs

    volumes: 
      - ./Logs:/app/Logs

    deploy:
      replicas: 1
      resources:
        limits:
          cpus: "0.5"
          memory: 256M
    
    networks:
      - monitoring

  loki:
    image: grafana/loki:main-dfba28c
    ports:
      - "3100:3100"

    deploy:
      replicas: 1
      resources:
        limits:
          cpus: "0.5"
          memory: 256M
    
    networks:
      - monitoring

  promtail:
    image: grafana/promtail:main-dfba28c
    volumes:
      - ./Logs:/var/log/myapp
      - ./promtail-config.yml:/etc/promtail/config.yml

    deploy:
      replicas: 1
      resources:
        limits:
          cpus: "0.5"
          memory: 256M
    
    networks:
      - monitoring

networks:
  monitoring: