2025-07-10 10:34:53.156 +03:00 [DBG] An 'IServiceProvider' was created for internal use by Entity Framework.
2025-07-10 10:34:54.890 +03:00 [DBG] Entity Framework Core 9.0.0 initialized 'ApplicationDbContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:9.0.0' with options: EngineType=SqlServer 
2025-07-10 10:34:55.269 +03:00 [DBG] Creating DbConnection.
2025-07-10 10:34:55.370 +03:00 [DBG] Created DbConnection. (93ms).
2025-07-10 10:34:55.384 +03:00 [DBG] Migrating using database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:34:55.401 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:34:55.851 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:34:55.870 +03:00 [DBG] Creating DbCommand for 'ExecuteNonQuery'.
2025-07-10 10:34:55.886 +03:00 [DBG] Created DbCommand for 'ExecuteNonQuery' (13ms).
2025-07-10 10:34:55.892 +03:00 [DBG] Initialized DbCommand for 'ExecuteNonQuery' (27ms).
2025-07-10 10:34:55.911 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-10 10:34:55.989 +03:00 [INF] Executed DbCommand (70ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-10 10:34:56.000 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:34:56.025 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (16ms).
2025-07-10 10:34:56.033 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:34:56.038 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:34:56.043 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-10 10:34:56.052 +03:00 [DBG] Creating DbCommand for 'ExecuteScalar'.
2025-07-10 10:34:56.054 +03:00 [DBG] Created DbCommand for 'ExecuteScalar' (2ms).
2025-07-10 10:34:56.056 +03:00 [DBG] Initialized DbCommand for 'ExecuteScalar' (4ms).
2025-07-10 10:34:56.060 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-07-10 10:34:56.129 +03:00 [INF] Executed DbCommand (69ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-07-10 10:34:56.286 +03:00 [DBG] Creating DbCommand for 'ExecuteNonQuery'.
2025-07-10 10:34:56.289 +03:00 [DBG] Created DbCommand for 'ExecuteNonQuery' (3ms).
2025-07-10 10:34:56.292 +03:00 [DBG] Initialized DbCommand for 'ExecuteNonQuery' (5ms).
2025-07-10 10:34:56.294 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-07-10 10:34:56.309 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-07-10 10:34:56.322 +03:00 [DBG] Beginning transaction with isolation level 'Unspecified'.
2025-07-10 10:34:56.340 +03:00 [DBG] Began transaction with isolation level 'ReadCommitted'.
2025-07-10 10:34:56.348 +03:00 [DBG] Creating DbCommand for 'ExecuteNonQuery'.
2025-07-10 10:34:56.351 +03:00 [DBG] Created DbCommand for 'ExecuteNonQuery' (3ms).
2025-07-10 10:34:56.357 +03:00 [DBG] Initialized DbCommand for 'ExecuteNonQuery' (8ms).
2025-07-10 10:34:56.359 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-10 10:34:56.367 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-10 10:34:56.372 +03:00 [DBG] Creating DbCommand for 'ExecuteScalar'.
2025-07-10 10:34:56.375 +03:00 [DBG] Created DbCommand for 'ExecuteScalar' (2ms).
2025-07-10 10:34:56.380 +03:00 [DBG] Initialized DbCommand for 'ExecuteScalar' (7ms).
2025-07-10 10:34:56.383 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-10 10:34:56.389 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-10 10:34:56.397 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-10 10:34:56.399 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (1ms).
2025-07-10 10:34:56.401 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (3ms).
2025-07-10 10:34:56.405 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-10 10:34:56.468 +03:00 [INF] Executed DbCommand (63ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-10 10:34:56.478 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:34:56.483 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 8ms reading results.
2025-07-10 10:34:56.506 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-07-10 10:34:56.511 +03:00 [DBG] Committing transaction.
2025-07-10 10:34:56.520 +03:00 [DBG] Committed transaction.
2025-07-10 10:34:56.524 +03:00 [DBG] Creating DbCommand for 'ExecuteScalar'.
2025-07-10 10:34:56.527 +03:00 [DBG] Created DbCommand for 'ExecuteScalar' (2ms).
2025-07-10 10:34:56.531 +03:00 [DBG] Initialized DbCommand for 'ExecuteScalar' (6ms).
2025-07-10 10:34:56.535 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-07-10 10:34:56.547 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-07-10 10:34:56.554 +03:00 [DBG] Disposing transaction.
2025-07-10 10:34:56.555 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:34:56.559 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (3ms).
2025-07-10 10:34:56.564 +03:00 [DBG] 'ApplicationDbContext' disposed.
2025-07-10 10:34:56.570 +03:00 [DBG] Disposing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:34:56.575 +03:00 [DBG] Disposed connection to database '' on server '' (4ms).
2025-07-10 10:34:56.671 +03:00 [DBG] Registered model binder providers, in the following order: ["Microsoft.AspNetCore.Mvc.Versioning.ApiVersionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BinderTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ServicesModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.HeaderModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FloatingPointTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.EnumTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.TryParseModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CancellationTokenModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ByteArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormFileModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormCollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.KeyValuePairModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DictionaryModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ComplexObjectModelBinderProvider"]
2025-07-10 10:34:57.582 +03:00 [DBG] Hosting starting
2025-07-10 10:34:57.604 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-10 10:34:57.620 +03:00 [DBG] Reading data from file 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-319f187e-4a98-49f6-8c46-784e4d26bef3.xml'.
2025-07-10 10:34:57.680 +03:00 [DBG] Found key {319f187e-4a98-49f6-8c46-784e4d26bef3}.
2025-07-10 10:34:57.706 +03:00 [DBG] Considering key {319f187e-4a98-49f6-8c46-784e4d26bef3} with expiration date 2025-08-15 09:54:18Z as default key.
2025-07-10 10:34:57.712 +03:00 [DBG] Forwarded activator type request from Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60 to Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60
2025-07-10 10:34:57.716 +03:00 [DBG] Decrypting secret element using Windows DPAPI.
2025-07-10 10:34:57.720 +03:00 [DBG] Forwarded activator type request from Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60 to Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60
2025-07-10 10:34:57.729 +03:00 [DBG] Opening CNG algorithm 'AES' from provider 'null' with chaining mode CBC.
2025-07-10 10:34:57.734 +03:00 [DBG] Opening CNG algorithm 'SHA256' from provider 'null' with HMAC.
2025-07-10 10:34:57.740 +03:00 [DBG] Using key {319f187e-4a98-49f6-8c46-784e4d26bef3} as the default key.
2025-07-10 10:34:57.743 +03:00 [DBG] Key ring with default key {319f187e-4a98-49f6-8c46-784e4d26bef3} was loaded during application startup.
2025-07-10 10:34:57.895 +03:00 [WRN] The WebRootPath was not found: C:\Users\<USER>\RiderProjects\Travel-Accommodation-Booking-Platform-F\Travel-Accommodation-Booking-Platform-F.API\wwwroot. Static files may be unavailable.
2025-07-10 10:34:57.906 +03:00 [DBG] Middleware configuration started with options: {AllowedHosts = *, AllowEmptyHosts = True, IncludeFailureMessage = True}
2025-07-10 10:34:57.910 +03:00 [DBG] Wildcard detected, all requests with hosts will be allowed.
2025-07-10 10:34:57.968 +03:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://[::1]:5000: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.
 ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-10 10:35:51.534 +03:00 [DBG] An 'IServiceProvider' was created for internal use by Entity Framework.
2025-07-10 10:35:53.683 +03:00 [DBG] Entity Framework Core 9.0.0 initialized 'ApplicationDbContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:9.0.0' with options: EngineType=SqlServer 
2025-07-10 10:35:54.087 +03:00 [DBG] Creating DbConnection.
2025-07-10 10:35:54.204 +03:00 [DBG] Created DbConnection. (108ms).
2025-07-10 10:35:54.221 +03:00 [DBG] Migrating using database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:35:54.241 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:35:54.737 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:35:54.766 +03:00 [DBG] Creating DbCommand for 'ExecuteNonQuery'.
2025-07-10 10:35:54.793 +03:00 [DBG] Created DbCommand for 'ExecuteNonQuery' (18ms).
2025-07-10 10:35:54.804 +03:00 [DBG] Initialized DbCommand for 'ExecuteNonQuery' (39ms).
2025-07-10 10:35:54.840 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-10 10:35:54.959 +03:00 [INF] Executed DbCommand (112ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-10 10:35:54.975 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:35:55.008 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (20ms).
2025-07-10 10:35:55.014 +03:00 [DBG] Opening connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:35:55.022 +03:00 [DBG] Opened connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:35:55.029 +03:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-07-10 10:35:55.041 +03:00 [DBG] Creating DbCommand for 'ExecuteScalar'.
2025-07-10 10:35:55.043 +03:00 [DBG] Created DbCommand for 'ExecuteScalar' (2ms).
2025-07-10 10:35:55.047 +03:00 [DBG] Initialized DbCommand for 'ExecuteScalar' (6ms).
2025-07-10 10:35:55.052 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-07-10 10:35:55.137 +03:00 [INF] Executed DbCommand (85ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-07-10 10:35:55.306 +03:00 [DBG] Creating DbCommand for 'ExecuteNonQuery'.
2025-07-10 10:35:55.309 +03:00 [DBG] Created DbCommand for 'ExecuteNonQuery' (3ms).
2025-07-10 10:35:55.313 +03:00 [DBG] Initialized DbCommand for 'ExecuteNonQuery' (7ms).
2025-07-10 10:35:55.316 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-07-10 10:35:55.332 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-07-10 10:35:55.348 +03:00 [DBG] Beginning transaction with isolation level 'Unspecified'.
2025-07-10 10:35:55.366 +03:00 [DBG] Began transaction with isolation level 'ReadCommitted'.
2025-07-10 10:35:55.374 +03:00 [DBG] Creating DbCommand for 'ExecuteNonQuery'.
2025-07-10 10:35:55.377 +03:00 [DBG] Created DbCommand for 'ExecuteNonQuery' (3ms).
2025-07-10 10:35:55.384 +03:00 [DBG] Initialized DbCommand for 'ExecuteNonQuery' (9ms).
2025-07-10 10:35:55.387 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-10 10:35:55.396 +03:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-10 10:35:55.403 +03:00 [DBG] Creating DbCommand for 'ExecuteScalar'.
2025-07-10 10:35:55.407 +03:00 [DBG] Created DbCommand for 'ExecuteScalar' (4ms).
2025-07-10 10:35:55.412 +03:00 [DBG] Initialized DbCommand for 'ExecuteScalar' (9ms).
2025-07-10 10:35:55.416 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-10 10:35:55.423 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-10 10:35:55.434 +03:00 [DBG] Creating DbCommand for 'ExecuteReader'.
2025-07-10 10:35:55.438 +03:00 [DBG] Created DbCommand for 'ExecuteReader' (3ms).
2025-07-10 10:35:55.441 +03:00 [DBG] Initialized DbCommand for 'ExecuteReader' (6ms).
2025-07-10 10:35:55.445 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-10 10:35:55.459 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-10 10:35:55.471 +03:00 [DBG] Closing data reader to 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:35:55.477 +03:00 [DBG] A data reader for 'TravelAccommodationPlatformDb' on server '*************,1433' is being disposed after spending 11ms reading results.
2025-07-10 10:35:55.515 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-07-10 10:35:55.520 +03:00 [DBG] Committing transaction.
2025-07-10 10:35:55.532 +03:00 [DBG] Committed transaction.
2025-07-10 10:35:55.536 +03:00 [DBG] Creating DbCommand for 'ExecuteScalar'.
2025-07-10 10:35:55.538 +03:00 [DBG] Created DbCommand for 'ExecuteScalar' (2ms).
2025-07-10 10:35:55.542 +03:00 [DBG] Initialized DbCommand for 'ExecuteScalar' (6ms).
2025-07-10 10:35:55.545 +03:00 [DBG] Executing DbCommand [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-07-10 10:35:55.556 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-07-10 10:35:55.564 +03:00 [DBG] Disposing transaction.
2025-07-10 10:35:55.566 +03:00 [DBG] Closing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:35:55.570 +03:00 [DBG] Closed connection to database 'TravelAccommodationPlatformDb' on server '*************,1433' (3ms).
2025-07-10 10:35:55.578 +03:00 [DBG] 'ApplicationDbContext' disposed.
2025-07-10 10:35:55.585 +03:00 [DBG] Disposing connection to database 'TravelAccommodationPlatformDb' on server '*************,1433'.
2025-07-10 10:35:55.591 +03:00 [DBG] Disposed connection to database '' on server '' (5ms).
2025-07-10 10:35:55.680 +03:00 [DBG] Registered model binder providers, in the following order: ["Microsoft.AspNetCore.Mvc.Versioning.ApiVersionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BinderTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ServicesModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.HeaderModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FloatingPointTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.EnumTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.TryParseModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CancellationTokenModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ByteArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormFileModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormCollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.KeyValuePairModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DictionaryModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ArrayModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinderProvider","Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ComplexObjectModelBinderProvider"]
2025-07-10 10:35:55.860 +03:00 [DBG] Hosting starting
2025-07-10 10:35:55.888 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-10 10:35:55.913 +03:00 [DBG] Reading data from file 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-319f187e-4a98-49f6-8c46-784e4d26bef3.xml'.
2025-07-10 10:35:55.934 +03:00 [DBG] Found key {319f187e-4a98-49f6-8c46-784e4d26bef3}.
2025-07-10 10:35:55.971 +03:00 [DBG] Considering key {319f187e-4a98-49f6-8c46-784e4d26bef3} with expiration date 2025-08-15 09:54:18Z as default key.
2025-07-10 10:35:55.979 +03:00 [DBG] Forwarded activator type request from Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60 to Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60
2025-07-10 10:35:55.982 +03:00 [DBG] Decrypting secret element using Windows DPAPI.
2025-07-10 10:35:56.002 +03:00 [DBG] Forwarded activator type request from Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60 to Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60
2025-07-10 10:35:56.010 +03:00 [DBG] Opening CNG algorithm 'AES' from provider 'null' with chaining mode CBC.
2025-07-10 10:35:56.016 +03:00 [DBG] Opening CNG algorithm 'SHA256' from provider 'null' with HMAC.
2025-07-10 10:35:56.026 +03:00 [DBG] Using key {319f187e-4a98-49f6-8c46-784e4d26bef3} as the default key.
2025-07-10 10:35:56.031 +03:00 [DBG] Key ring with default key {319f187e-4a98-49f6-8c46-784e4d26bef3} was loaded during application startup.
2025-07-10 10:35:56.307 +03:00 [WRN] The WebRootPath was not found: C:\Users\<USER>\RiderProjects\Travel-Accommodation-Booking-Platform-F\Travel-Accommodation-Booking-Platform-F.API\wwwroot. Static files may be unavailable.
2025-07-10 10:35:56.321 +03:00 [DBG] Middleware configuration started with options: {AllowedHosts = *, AllowEmptyHosts = True, IncludeFailureMessage = True}
2025-07-10 10:35:56.325 +03:00 [DBG] Wildcard detected, all requests with hosts will be allowed.
2025-07-10 10:35:56.361 +03:00 [INF] Now listening on: http://localhost:5000
2025-07-10 10:35:56.366 +03:00 [DBG] Loaded hosting startup assembly Travel-Accommodation-Booking-Platform-F.API
2025-07-10 10:35:56.369 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-10 10:35:56.371 +03:00 [INF] Hosting environment: Development
2025-07-10 10:35:56.373 +03:00 [INF] Content root path: C:\Users\<USER>\RiderProjects\Travel-Accommodation-Booking-Platform-F\Travel-Accommodation-Booking-Platform-F.API
2025-07-10 10:35:56.376 +03:00 [DBG] Hosting started
2025-07-10 11:07:46.722 +03:00 [INF] Application is shutting down...
2025-07-10 11:07:46.724 +03:00 [DBG] Hosting stopping
2025-07-10 11:07:46.758 +03:00 [DBG] Hosting stopped
