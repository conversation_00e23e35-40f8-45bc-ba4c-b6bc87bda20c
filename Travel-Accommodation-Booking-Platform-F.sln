﻿
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Travel-Accommodation-Booking-Platform-F.API", "Travel-Accommodation-Booking-Platform-F.API\Travel-Accommodation-Booking-Platform-F.API.csproj", "{AB11488D-502E-421A-862F-C3DDF6D9A5D9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Travel-Accommodation-Booking-Platform-F.Application", "Travel-Accommodation-Booking-Platform-F.Application\Travel-Accommodation-Booking-Platform-F.Application.csproj", "{32994305-DBF3-4A6B-8529-E62D440057F5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Travel-Accommodation-Booking-Platform-F.Domain", "Travel-Accommodation-Booking-Platform-F.Domain\Travel-Accommodation-Booking-Platform-F.Domain.csproj", "{92FB07DC-8430-4E3E-8BC3-55B3C04B8F26}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Travel-Accommodation-Booking-Platform-F.Infrastructure", "Travel-Accommodation-Booking-Platform-F.Infrastructure\Travel-Accommodation-Booking-Platform-F.Infrastructure.csproj", "{8EEC268B-2ADA-4BF2-994D-A9EA7D7E1B1A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Travel-Accommodation-Booking-Platform-F.UnitTests", "Travel-Accommodation-Booking-Platform-F.UnitTests\Travel-Accommodation-Booking-Platform-F.UnitTests.csproj", "{71C42671-EC59-4B5B-B7B9-8547DB7FA236}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Travel-Accommodation-Booking-Platform-F.IntegrationTests", "Travel-Accommodation-Booking-Platform-F.IntegrationTests\Travel-Accommodation-Booking-Platform-F.IntegrationTests.csproj", "{CD7833EC-9BAB-4B91-BF26-C774C30E98A4}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{AB11488D-502E-421A-862F-C3DDF6D9A5D9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB11488D-502E-421A-862F-C3DDF6D9A5D9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB11488D-502E-421A-862F-C3DDF6D9A5D9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB11488D-502E-421A-862F-C3DDF6D9A5D9}.Release|Any CPU.Build.0 = Release|Any CPU
		{32994305-DBF3-4A6B-8529-E62D440057F5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{32994305-DBF3-4A6B-8529-E62D440057F5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{32994305-DBF3-4A6B-8529-E62D440057F5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{32994305-DBF3-4A6B-8529-E62D440057F5}.Release|Any CPU.Build.0 = Release|Any CPU
		{92FB07DC-8430-4E3E-8BC3-55B3C04B8F26}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{92FB07DC-8430-4E3E-8BC3-55B3C04B8F26}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{92FB07DC-8430-4E3E-8BC3-55B3C04B8F26}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{92FB07DC-8430-4E3E-8BC3-55B3C04B8F26}.Release|Any CPU.Build.0 = Release|Any CPU
		{8EEC268B-2ADA-4BF2-994D-A9EA7D7E1B1A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8EEC268B-2ADA-4BF2-994D-A9EA7D7E1B1A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8EEC268B-2ADA-4BF2-994D-A9EA7D7E1B1A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8EEC268B-2ADA-4BF2-994D-A9EA7D7E1B1A}.Release|Any CPU.Build.0 = Release|Any CPU
		{71C42671-EC59-4B5B-B7B9-8547DB7FA236}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{71C42671-EC59-4B5B-B7B9-8547DB7FA236}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{71C42671-EC59-4B5B-B7B9-8547DB7FA236}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{71C42671-EC59-4B5B-B7B9-8547DB7FA236}.Release|Any CPU.Build.0 = Release|Any CPU
		{CD7833EC-9BAB-4B91-BF26-C774C30E98A4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CD7833EC-9BAB-4B91-BF26-C774C30E98A4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CD7833EC-9BAB-4B91-BF26-C774C30E98A4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CD7833EC-9BAB-4B91-BF26-C774C30E98A4}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
