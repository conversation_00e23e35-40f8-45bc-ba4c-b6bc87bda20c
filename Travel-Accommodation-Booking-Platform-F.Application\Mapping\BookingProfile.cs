﻿using AutoMapper;
using Travel_Accommodation_Booking_Platform_F.Application.DTOs.ReadDTOs;
using Travel_Accommodation_Booking_Platform_F.Application.DTOs.WriteDTOs;
using Travel_Accommodation_Booking_Platform_F.Domain.Entities;

namespace Travel_Accommodation_Booking_Platform_F.Application.Mapping;

public class BookingProfile : Profile
{
    public BookingProfile()
    {
        CreateMap<BookingWriteDto, Booking>();
        CreateMap<BookingPatchDto, Booking>();
        CreateMap<Booking, BookingReadDto>();
        CreateMap<Booking, BookingWriteDto>();
    }
}