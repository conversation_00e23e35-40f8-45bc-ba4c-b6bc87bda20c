﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <RootNamespace>Travel_Accommodation_Booking_Platform_F.Infrastructure</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Travel-Accommodation-Booking-Platform-F.Application\Travel-Accommodation-Booking-Platform-F.Application.csproj"/>
        <ProjectReference Include="..\Travel-Accommodation-Booking-Platform-F.Domain\Travel-Accommodation-Booking-Platform-F.Domain.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="MailKit" Version="4.13.0"/>
        <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="9.0.0"/>
    </ItemGroup>

</Project>
